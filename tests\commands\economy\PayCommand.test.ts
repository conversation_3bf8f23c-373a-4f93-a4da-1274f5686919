/**
 * PayCommand Test Suite
 * Comprehensive tests for the /pay command functionality
 */

import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { PayCommand } from '../../../src/commands/economy/PayCommand';
import { CommandContext } from '../../../src/core/interfaces';
import { ValidationError, DatabaseError, RateLimitError } from '../../../src/utils/errorHandler';
import User from '../../../src/models/User';
import Transaction from '../../../src/models/Transaction';

// Mock dependencies
jest.mock('../../../src/utils/embedBuilder', () => ({
  createServerSuccessEmbed: jest.fn().mockResolvedValue({
    setDescription: jest.fn().mockReturnThis(),
    addFields: jest.fn().mockReturnThis()
  }),
  addUserInfo: jest.fn(),
  formatServerCoins: jest.fn().mockImplementation((guildId, amount) => `${amount} coins`),
  EMOJIS: {
    ECONOMY: { TRANSFER: '💸', BALANCE: '💰', COINS: '🪙' },
    ACTIONS: { SENDER: '👤', TARGET: '🎯' }
  }
}));

jest.mock('../../../src/services/economyService', () => ({
  ensureUser: jest.fn()
}));

jest.mock('../../../src/utils/validation/ValidationUtils', () => ({
  RateLimitValidator: {
    validateRateLimit: jest.fn()
  }
}));

describe('PayCommand', () => {
  let mongoServer: MongoMemoryServer;
  let payCommand: PayCommand;
  let mockInteraction: any;
  let mockTargetUser: any;
  let mockGuild: any;
  let mockContext: CommandContext;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear database
    await User.deleteMany({});
    await Transaction.deleteMany({});

    // Create PayCommand instance
    payCommand = new PayCommand();

    // Create mock users
    mockTargetUser = {
      id: '987654321098765432',
      username: 'recipient',
      displayName: 'Recipient User',
      tag: 'recipient#1234',
      bot: false,
      send: jest.fn().mockResolvedValue(undefined)
    };

    // Create mock guild
    mockGuild = {
      id: '123456789012345678',
      members: {
        fetch: jest.fn().mockResolvedValue({
          id: mockTargetUser.id,
          user: mockTargetUser
        })
      }
    };

    // Create mock interaction
    mockInteraction = {
      user: {
        id: '123456789012345678',
        username: 'sender',
        displayName: 'Sender User',
        tag: 'sender#5678'
      },
      guild: mockGuild,
      options: {
        getUser: jest.fn().mockReturnValue(mockTargetUser),
        getInteger: jest.fn().mockReturnValue(100)
      },
      reply: jest.fn().mockResolvedValue(undefined)
    };

    // Create mock context
    mockContext = {
      interaction: mockInteraction
    } as CommandContext;

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('Command Structure', () => {
    test('should have correct command data', () => {
      expect(payCommand.data.name).toBe('pay');
      expect(payCommand.data.description).toBe('Transfer coins to another user');
    });

    test('should have correct category and settings', () => {
      expect(payCommand.category).toBe('ECONOMY');
      expect(payCommand.cooldown).toBe(5);
      expect(payCommand.requiredFeatures).toContain('ECONOMY_SYSTEM');
    });
  });

  describe('Input Validation', () => {
    test('should reject self-transfers', async () => {
      mockInteraction.options.getUser.mockReturnValue({
        ...mockTargetUser,
        id: mockInteraction.user.id
      });

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        ValidationError
      );
    });

    test('should reject transfers to bots', async () => {
      mockInteraction.options.getUser.mockReturnValue({
        ...mockTargetUser,
        bot: true
      });

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        'You cannot send coins to bots'
      );
    });

    test('should reject zero amounts', async () => {
      mockInteraction.options.getInteger.mockReturnValue(0);

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        'Amount must be greater than 0'
      );
    });

    test('should reject negative amounts', async () => {
      mockInteraction.options.getInteger.mockReturnValue(-50);

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        'Amount must be greater than 0'
      );
    });

    test('should reject amounts exceeding maximum', async () => {
      mockInteraction.options.getInteger.mockReturnValue(2000000);

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        'Amount cannot exceed'
      );
    });

    test('should reject invalid Discord IDs', async () => {
      mockInteraction.user.id = 'invalid-id';

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        'Invalid sender Discord ID format'
      );
    });
  });

  describe('Database Operations', () => {
    beforeEach(async () => {
      // Create sender with sufficient balance
      await User.create({
        discordId: mockInteraction.user.id,
        guildId: mockGuild.id,
        balance: 1000
      });
    });

    test('should successfully transfer coins between users', async () => {
      const amount = 100;
      mockInteraction.options.getInteger.mockReturnValue(amount);

      await payCommand.execute(mockContext);

      // Check sender balance
      const sender = await User.findOne({
        discordId: mockInteraction.user.id,
        guildId: mockGuild.id
      });
      expect(sender?.balance).toBe(900);

      // Check recipient balance
      const recipient = await User.findOne({
        discordId: mockTargetUser.id,
        guildId: mockGuild.id
      });
      expect(recipient?.balance).toBe(100);

      // Check transaction records
      const transactions = await Transaction.find({
        guildId: mockGuild.id,
        type: 'pay'
      });
      expect(transactions).toHaveLength(2);
    });

    test('should reject transfer with insufficient balance', async () => {
      const amount = 2000; // More than sender's balance
      mockInteraction.options.getInteger.mockReturnValue(amount);

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        'Insufficient balance'
      );

      // Verify no changes to database
      const sender = await User.findOne({
        discordId: mockInteraction.user.id,
        guildId: mockGuild.id
      });
      expect(sender?.balance).toBe(1000); // Unchanged
    });

    test('should handle database connection errors', async () => {
      // Disconnect from database
      await mongoose.disconnect();

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        DatabaseError
      );

      // Reconnect for cleanup
      const mongoUri = mongoServer.getUri();
      await mongoose.connect(mongoUri);
    });
  });

  describe('Security Features', () => {
    beforeEach(async () => {
      await User.create({
        discordId: mockInteraction.user.id,
        guildId: mockGuild.id,
        balance: 100000
      });
    });

    test('should apply rate limiting for high-value transactions', async () => {
      const { RateLimitValidator } = require('../../../src/utils/validation/ValidationUtils');
      RateLimitValidator.validateRateLimit.mockImplementation(() => {
        throw new ValidationError('Rate limit exceeded');
      });

      mockInteraction.options.getInteger.mockReturnValue(50000);

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        RateLimitError
      );
    });

    test('should verify target user is in the same guild', async () => {
      mockGuild.members.fetch.mockRejectedValue(new Error('User not found'));

      await expect(payCommand.execute(mockContext)).rejects.toThrow(
        'The target user is not a member of this server'
      );
    });
  });

  describe('Transaction Atomicity', () => {
    beforeEach(async () => {
      await User.create({
        discordId: mockInteraction.user.id,
        guildId: mockGuild.id,
        balance: 1000
      });
    });

    test('should rollback on transaction failure', async () => {
      // Mock Transaction.create to fail
      const originalCreate = Transaction.create;
      Transaction.create = jest.fn().mockRejectedValue(new Error('Transaction failed'));

      const amount = 100;
      mockInteraction.options.getInteger.mockReturnValue(amount);

      await expect(payCommand.execute(mockContext)).rejects.toThrow();

      // Verify sender balance is unchanged
      const sender = await User.findOne({
        discordId: mockInteraction.user.id,
        guildId: mockGuild.id
      });
      expect(sender?.balance).toBe(1000);

      // Verify recipient doesn't exist
      const recipient = await User.findOne({
        discordId: mockTargetUser.id,
        guildId: mockGuild.id
      });
      expect(recipient).toBeNull();

      // Restore original method
      Transaction.create = originalCreate;
    });
  });

  describe('User Experience', () => {
    beforeEach(async () => {
      await User.create({
        discordId: mockInteraction.user.id,
        guildId: mockGuild.id,
        balance: 1000
      });
    });

    test('should send success message with correct balance', async () => {
      const amount = 100;
      mockInteraction.options.getInteger.mockReturnValue(amount);

      await payCommand.execute(mockContext);

      expect(mockInteraction.reply).toHaveBeenCalledWith({
        embeds: expect.any(Array),
        ephemeral: false
      });
    });

    test('should attempt to notify recipient via DM', async () => {
      const amount = 100;
      mockInteraction.options.getInteger.mockReturnValue(amount);

      await payCommand.execute(mockContext);

      expect(mockTargetUser.send).toHaveBeenCalled();
    });

    test('should handle DM failure gracefully', async () => {
      mockTargetUser.send.mockRejectedValue(new Error('Cannot send DM'));

      const amount = 100;
      mockInteraction.options.getInteger.mockReturnValue(amount);

      // Should not throw error even if DM fails
      await expect(payCommand.execute(mockContext)).resolves.not.toThrow();
    });
  });
});
