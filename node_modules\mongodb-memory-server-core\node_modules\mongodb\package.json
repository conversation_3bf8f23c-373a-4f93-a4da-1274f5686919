{"name": "mongodb", "version": "5.9.2", "description": "The official MongoDB driver for Node.js", "main": "lib/index.js", "files": ["lib", "src", "etc/prepare.js", "mongodb.d.ts", "tsconfig.json"], "types": "mongodb.d.ts", "repository": {"type": "git", "url": "**************:mongodb/node-mongodb-native.git"}, "keywords": ["mongodb", "driver", "official"], "author": {"name": "The MongoDB NodeJS Team", "email": "<EMAIL>"}, "dependencies": {"bson": "^5.5.0", "mongodb-connection-string-url": "^2.6.0", "socks": "^2.7.1"}, "optionalDependencies": {"@mongodb-js/saslprep": "^1.1.0"}, "peerDependencies": {"@aws-sdk/credential-providers": "^3.188.0", "@mongodb-js/zstd": "^1.0.0", "kerberos": "^1.0.0 || ^2.0.0", "mongodb-client-encryption": ">=2.3.0 <3", "snappy": "^7.2.2"}, "peerDependenciesMeta": {"@aws-sdk/credential-providers": {"optional": true}, "@mongodb-js/zstd": {"optional": true}, "kerberos": {"optional": true}, "snappy": {"optional": true}, "mongodb-client-encryption": {"optional": true}}, "devDependencies": {"@iarna/toml": "^2.2.5", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@microsoft/api-extractor": "^7.35.1", "@microsoft/tsdoc-config": "^0.16.2", "@mongodb-js/zstd": "^1.1.0", "@octokit/core": "^4.2.4", "@types/chai": "^4.3.5", "@types/chai-subset": "^1.3.3", "@types/express": "^4.17.17", "@types/kerberos": "^1.1.2", "@types/mocha": "^10.0.1", "@types/node": "^20.1.0", "@types/semver": "^7.5.0", "@types/sinon": "^10.0.14", "@types/sinon-chai": "^3.2.9", "@types/whatwg-url": "^11.0.0", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "chai": "^4.3.7", "chai-subset": "^1.6.0", "chalk": "^4.1.2", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tsdoc": "^0.2.17", "express": "^4.18.2", "js-yaml": "^4.1.0", "mocha": "^10.2.0", "mocha-sinon": "^2.1.2", "mongodb-legacy": "^5.0.0", "nyc": "^15.1.0", "prettier": "^2.8.8", "semver": "^7.5.0", "sinon": "^15.0.4", "sinon-chai": "^3.7.0", "snappy": "^7.2.2", "source-map-support": "^0.5.21", "ts-node": "^10.9.1", "tsd": "^0.28.1", "typescript": "^5.0.4", "typescript-cached-transpile": "^0.0.6", "v8-heapsnapshot": "^1.2.0", "yargs": "^17.7.2"}, "license": "Apache-2.0", "engines": {"node": ">=14.20.1"}, "bugs": {"url": "https://jira.mongodb.org/projects/NODE/issues/"}, "homepage": "https://github.com/mongodb/node-mongodb-native", "scripts": {"build:evergreen": "node .evergreen/generate_evergreen_tasks.js", "build:ts": "node ./node_modules/typescript/bin/tsc", "build:dts": "npm run build:ts && api-extractor run && node etc/clean_definition_files.cjs", "build:docs": "./etc/docs/build.ts", "build:typedoc": "typedoc", "build:nightly": "node ./.github/scripts/nightly.mjs", "check:bench": "node test/benchmarks/driverBench", "check:coverage": "nyc npm run test:all", "check:integration-coverage": "nyc npm run check:test", "check:lambda": "mocha --config test/mocha_lambda.json test/integration/node-specific/examples/handler.test.js", "check:lambda:aws": "mocha --config test/mocha_lambda.json test/integration/node-specific/examples/aws_handler.test.js", "check:lint": "npm run build:dts && npm run check:dts && npm run check:eslint && npm run check:tsd", "check:eslint": "eslint -v && eslint --max-warnings=0 --ext '.js,.ts' src test", "check:tsd": "tsd --version && tsd", "check:dependencies": "mocha test/action/dependency.test.ts", "check:dts": "node ./node_modules/typescript/bin/tsc --noEmit mongodb.d.ts && tsd", "check:search-indexes": "nyc mocha --config test/mocha_mongodb.json test/manual/search-index-management.spec.test.ts", "check:test": "mocha --config test/mocha_mongodb.json test/integration", "check:unit": "mocha test/unit", "check:ts": "node ./node_modules/typescript/bin/tsc -v && node ./node_modules/typescript/bin/tsc --noEmit", "check:atlas": "mocha --config test/manual/mocharc.json test/manual/atlas_connectivity.test.js", "check:adl": "mocha --config test/mocha_mongodb.json test/manual/atlas-data-lake-testing", "check:aws": "nyc mocha --config test/mocha_mongodb.json test/integration/auth/mongodb_aws.test.ts", "check:oidc": "mocha --config test/mocha_mongodb.json test/manual/mongodb_oidc.prose.test.ts", "check:oidc-azure": "mocha --config test/mocha_mongodb.json test/integration/auth/mongodb_oidc_azure.prose.test.ts", "check:ocsp": "mocha --config test/manual/mocharc.json test/manual/ocsp_support.test.js", "check:kerberos": "nyc mocha --config test/manual/mocharc.json test/manual/kerberos.test.ts", "check:tls": "mocha --config test/manual/mocharc.json test/manual/tls_support.test.js", "check:ldap": "nyc mocha --config test/manual/mocharc.json test/manual/ldap.test.js", "check:socks5": "mocha --config test/manual/mocharc.json test/manual/socks5.test.ts", "check:csfle": "mocha --config test/mocha_mongodb.json test/integration/client-side-encryption", "check:snappy": "mocha test/unit/assorted/snappy.test.js", "fix:eslint": "npm run check:eslint -- --fix", "prepare": "node etc/prepare.js", "preview:docs": "ts-node etc/docs/preview.ts", "test": "npm run check:lint && npm run test:all", "test:all": "npm run check:unit && npm run check:test", "update:docs": "npm run build:docs -- --yes"}, "tsd": {"directory": "test/types", "compilerOptions": {"strict": true, "target": "esnext", "module": "commonjs", "moduleResolution": "node"}}}