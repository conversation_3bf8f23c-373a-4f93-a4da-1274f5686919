{"version": 3, "file": "delete.js", "sourceRoot": "", "sources": ["../../src/operations/delete.ts"], "names": [], "mappings": ";;;AAEA,oCAAqE;AAKrE,uCAImB;AACnB,2CAA+D;AAkC/D,gBAAgB;AAChB,MAAa,eAAgB,SAAQ,kCAAsC;IAIzE,YAAY,EAAoB,EAAE,UAA6B,EAAE,OAAsB;QACrF,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,IAAa,aAAa;QACxB,IAAI,KAAK,CAAC,aAAa,KAAK,KAAK,EAAE;YACjC,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/E,CAAC;IAEQ,eAAe,CACtB,MAAc,EACd,OAAkC,EAClC,QAAkB;QAElB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,OAAO,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9E,MAAM,OAAO,GAAa;YACxB,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,UAAU;YAC1B,OAAO,EAAE,IAAI,CAAC,UAAU;YACxB,OAAO;SACR,CAAC;QAEF,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;SAC3B;QAED,iEAAiE;QACjE,gDAAgD;QAChD,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YACjC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SACnC;QAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC;QAC3E,IAAI,mBAAmB,EAAE;YACvB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;gBACjD,iEAAiE;gBACjE,QAAQ,CAAC,IAAI,+BAAuB,CAAC,kDAAkD,CAAC,CAAC,CAAC;gBAC1F,OAAO;aACR;SACF;QAED,KAAK,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACnE,CAAC;CACF;AArDD,0CAqDC;AAED,MAAa,kBAAmB,SAAQ,eAAe;IACrD,YAAY,UAAsB,EAAE,MAAgB,EAAE,OAAsB;QAC1E,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAClG,CAAC;IAEQ,eAAe,CACtB,MAAc,EACd,OAAkC,EAClC,QAAgC;QAEhC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI;gBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC7C,IAAI,GAAG,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,IAAI,wBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,IAAI,GAAG,CAAC,WAAW;gBAAE,OAAO,QAAQ,CAAC,IAAI,wBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/E,IAAI,IAAI,CAAC,OAAO;gBAAE,OAAO,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YAElD,QAAQ,CAAC,SAAS,EAAE;gBAClB,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI;gBAChD,YAAY,EAAE,GAAG,CAAC,CAAC;aACpB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAtBD,gDAsBC;AAED,MAAa,mBAAoB,SAAQ,eAAe;IACtD,YAAY,UAAsB,EAAE,MAAgB,EAAE,OAAsB;QAC1E,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IACjF,CAAC;IAEQ,eAAe,CACtB,MAAc,EACd,OAAkC,EAClC,QAAgC;QAEhC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI;gBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC7C,IAAI,GAAG,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,IAAI,wBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,IAAI,GAAG,CAAC,WAAW;gBAAE,OAAO,QAAQ,CAAC,IAAI,wBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/E,IAAI,IAAI,CAAC,OAAO;gBAAE,OAAO,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YAElD,QAAQ,CAAC,SAAS,EAAE;gBAClB,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI;gBAChD,YAAY,EAAE,GAAG,CAAC,CAAC;aACpB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAtBD,kDAsBC;AAED,SAAgB,mBAAmB,CACjC,MAAgB,EAChB,OAA2C;IAE3C,MAAM,EAAE,GAAoB;QAC1B,CAAC,EAAE,MAAM;QACT,KAAK,EAAE,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC7D,CAAC;IAEF,IAAI,OAAO,CAAC,SAAS,EAAE;QACrB,EAAE,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;KAClC;IAED,IAAI,OAAO,CAAC,IAAI,EAAE;QAChB,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;KACxB;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAlBD,kDAkBC;AAED,IAAA,yBAAa,EAAC,eAAe,EAAE,CAAC,kBAAM,CAAC,SAAS,EAAE,kBAAM,CAAC,eAAe,CAAC,CAAC,CAAC;AAC3E,IAAA,yBAAa,EAAC,kBAAkB,EAAE;IAChC,kBAAM,CAAC,SAAS;IAChB,kBAAM,CAAC,eAAe;IACtB,kBAAM,CAAC,WAAW;IAClB,kBAAM,CAAC,cAAc;CACtB,CAAC,CAAC;AACH,IAAA,yBAAa,EAAC,mBAAmB,EAAE;IACjC,kBAAM,CAAC,eAAe;IACtB,kBAAM,CAAC,WAAW;IAClB,kBAAM,CAAC,cAAc;CACtB,CAAC,CAAC"}