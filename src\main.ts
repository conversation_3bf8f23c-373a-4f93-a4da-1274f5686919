
// Ensure .env variables are loaded before anything else
import 'dotenv/config';

// DEPLOYMENT VERIFICATION MARKER - 2025-08-02T20:47:31.237Z
console.log('🔍 DEPLOYMENT VERIFICATION: Poll system rebuilt at 2025-08-02T20:47:31.237Z');
console.log('🎯 NEW POLL SYSTEM ACTIVE - If you see this, the rebuilt system is running');
console.log('='.repeat(60));

/**
 * Main Application Entry Point
 * Refactored main entry point using the new modular architecture
 *
 * This entry point now includes automated build and deployment processes
 * for self-contained deployment on platforms like Discloud.
 */

import { startApplication } from './core/application';
import { EventManager } from './events';
import { EconomyService } from './services/economy/EconomyService';
import { RoleService } from './services/role/RoleService';
import { TradeService } from './services/trade/TradeService';
import { DisputeService } from './services/trade/DisputeService';
import { TradeBackgroundService } from './services/trade/TradeBackgroundService';
import { ElectionService } from './services/election/ElectionService';
import { SalaryService } from './services/salary/SalaryService';
import { SuggestionService } from './services/suggestion/SuggestionService';
import { PollService } from './services/poll/PollService';
import { MemoryOptimizationService } from './services/MemoryOptimizationService';
import { MemoryAnalyzer } from './utils/memoryAnalyzer';
import { initializeMemoryConstrainedMode } from './config/memoryConstrainedMode';
import { isProduction } from './config/environment';
import { getLogger } from './core/logger';
import ServerConfigurationService from './services/serverConfigurationService';
// Auto-deployment imports
import {
  isProductionEnvironment,
  needsBuild,
  runBuild,
  deployCommands,
  deployRoleCommands,
  validateEnvironment,
  checkDependencies
} from './utils/auto-deployment';
import fs from 'fs';
import path from 'path';

/**
 * Main application startup function
 */

// ===== REBUILT POLL SYSTEM DEBUG MARKERS =====
console.log('🔍 DEPLOYMENT VERIFICATION: Poll system rebuilt at 2025-08-02T20:50:39.064Z');
console.log('🎯 NEW POLL SYSTEM ACTIVE - If you see this, the rebuilt system is running');
console.log('📊 Debug markers will help verify which code is actually executing');
console.log('='.repeat(60));

async function main(): Promise<void> {
  // Initialize memory-constrained mode first
  initializeMemoryConstrainedMode();

  // Reduced startup delay for faster initialization and lower memory footprint
  const startupLogger = getLogger();
  const isMemoryConstrained = process.env.MEMORY_CONSTRAINED_MODE === 'true';
  const delay = isMemoryConstrained ? 2000 : 5000; // Even shorter delay in memory mode

  startupLogger.info(`[Main] Waiting ${delay/1000} seconds before initialization (${isMemoryConstrained ? 'memory-constrained' : 'optimized'} mode)...`);
  await new Promise(resolve => setTimeout(resolve, delay));
  startupLogger.info('[Main] Startup delay complete. Continuing with initialization.');

  // Only print essential environment variables in production to reduce memory usage
  if (!isProduction) {
    // eslint-disable-next-line no-console
    console.log('[DEBUG] process.env:', process.env);
  } else {
    // In production, only log essential info to reduce memory footprint
    startupLogger.info('[Main] Running in production mode - debug logging disabled');
  }

  // Check for required environment variables
  const requiredEnv = ['BOT_TOKEN', 'MONGODB_URI'];
  const missingEnv = requiredEnv.filter((key) => !process.env[key]);
  if (missingEnv.length > 0) {
    // eslint-disable-next-line no-console
    console.error('[ENV][Missing] The following required environment variables are missing:', missingEnv);
    throw new Error(`Missing required environment variables: ${missingEnv.join(', ')}`);
  }

  // Auto-deployment process for self-contained hosting
  await runAutoDeploymentProcess();

  const logger = getLogger();

  try {
    logger.info('[Main] Starting Economy Bot with refactored architecture...');

    // Initialize the application
    const app = await startApplication();

    // Register additional services
    await registerServices(app);

    // Load and register commands
    await loadCommands(app);

    // Initialize event handlers
    const eventManager = new EventManager(app);
    eventManager.initialize();

    // Initialize server configurations for existing guilds
    await initializeServerConfigurations(app);

    // Make client globally available for memory analysis
    global.discordClient = app.client;

    // Perform initial memory analysis
    const memoryAnalyzer = MemoryAnalyzer.getInstance();
    memoryAnalyzer.logMemoryAnalysis();

    // Start memory monitoring in development
    if (!isProduction) {
      memoryAnalyzer.startMemoryMonitoring(60000); // Every minute in dev
    }

    logger.info('[Main] Economy Bot started successfully!');
    logger.info(`[Main] Bot is ready as ${app.client.user?.tag}`);

  } catch (error) {
    // Print any startup error directly to the console for visibility
    // eslint-disable-next-line no-console
    console.error('[MAIN][Raw Error]', error);
    logger.error('[Main] Failed to start application', { error });
    process.exit(1);
  }
}

/**
 * Run automated build and deployment processes
 * This ensures the bot is self-contained and can deploy without manual intervention
 */
async function runAutoDeploymentProcess(): Promise<void> {
  const logger = getLogger();

  try {
    logger.info('[Main] Starting auto-deployment process...');

    // Validate environment variables
    const envValidation = validateEnvironment();
    if (!envValidation.valid) {
      logger.error('[Main] Environment validation failed', { missing: envValidation.missing });
      throw new Error(`Missing required environment variables: ${envValidation.missing.join(', ')}`);
    }
    logger.info('[Main] Environment variables validated successfully');

    // Check dependencies
    const depCheck = await checkDependencies();
    if (!depCheck.valid) {
      logger.error('[Main] Dependency check failed', { issues: depCheck.issues });

      // In production, fail fast on dependency issues
      if (isProductionEnvironment()) {
        throw new Error(`Dependency issues: ${depCheck.issues.join(', ')}`);
      } else {
        logger.warn('[Main] Continuing despite dependency issues (development mode)');
      }
    } else {
      logger.info('[Main] Dependencies validated successfully');
    }

    // Check if we're in a production environment that needs auto-deployment
    const isProduction = isProductionEnvironment();
    logger.info(`[Main] Production environment detected: ${isProduction}`);

    // Always check if build is needed, regardless of environment
    const buildNeeded = await needsBuild();
    logger.info(`[Main] Build needed: ${buildNeeded}`);

    if (buildNeeded) {
      logger.info('[Main] Running TypeScript build process...');

      // Check if auto-deployment is disabled
      if (process.env.DISABLE_AUTO_DEPLOY === 'true') {
        logger.warn('[Main] Auto-deployment disabled - skipping build process');
        logger.warn('[Main] Ensure dist/ folder contains pre-built files');
      } else {
        try {
          const buildSuccess = await runBuild();

          if (!buildSuccess) {
            const errorMsg = 'Build process failed - cannot continue startup';
            logger.error(`[Main] ${errorMsg}`);
            logger.error('[Main] This may be due to Discloud environment constraints');
            logger.error('[Main] Consider setting DISABLE_AUTO_DEPLOY=true and uploading pre-built files');

            // In production, provide more graceful handling
            if (isProduction) {
              throw new Error(errorMsg);
            } else {
              logger.warn('[Main] Continuing despite build failure (development mode)');
            }
          } else {
            logger.info('[Main] Build process completed successfully');
          }
        } catch (buildError) {
          logger.error('[Main] Build process encountered an error', {
            error: buildError.message,
            stack: buildError.stack,
            nodeVersion: process.version,
            cwd: process.cwd(),
            isDiscloud: process.cwd().includes('/home/<USER>')
          });

          if (isProduction) {
            throw buildError;
          } else {
            logger.warn('[Main] Continuing despite build error (development mode)');
          }
        }
      }
    } else {
      logger.info('[Main] Build not needed, using existing compiled files');
    }

    // Deploy commands if environment variables are available
    if (process.env.CLIENT_ID && process.env.BOT_TOKEN) {
      logger.info('[Main] Deploying slash commands...');

      const commandsDeployed = await deployCommands();
      if (!commandsDeployed) {
        const warningMsg = 'Command deployment failed';
        logger.warn(`[Main] ${warningMsg}, but continuing startup...`);

        // In production, we might want to fail on command deployment failure
        // but for now, we'll continue to allow the bot to start
        if (isProduction) {
          logger.warn('[Main] Command deployment failed in production - bot may not function correctly');
        }
      } else {
        logger.info('[Main] Slash commands deployed successfully');
      }

      // Deploy role commands
      logger.info('[Main] Deploying role commands...');
      const roleCommandsDeployed = await deployRoleCommands();
      if (!roleCommandsDeployed) {
        const warningMsg = 'Role command deployment failed';
        logger.warn(`[Main] ${warningMsg}, but continuing startup...`);

        if (isProduction) {
          logger.warn('[Main] Role command deployment failed in production - some features may not work');
        }
      } else {
        logger.info('[Main] Role commands deployed successfully');
      }
    } else {
      logger.warn('[Main] Skipping command deployment - CLIENT_ID or BOT_TOKEN not available');
    }

    logger.info('[Main] Auto-deployment process completed successfully');
  } catch (error) {
    logger.error('[Main] Auto-deployment process failed', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      isProduction: isProductionEnvironment()
    });

    // In production, we want to fail fast if critical deployment fails
    if (isProductionEnvironment()) {
      // eslint-disable-next-line no-console
      console.error('[MAIN][CRITICAL] Auto-deployment failed in production:', error);
      throw error;
    } else {
      // In development, we can continue even if deployment fails
      logger.warn('[Main] Continuing startup despite deployment failure (development mode)');
      // eslint-disable-next-line no-console
      console.warn('[MAIN][DEV] Auto-deployment failed, but continuing in development mode:', error instanceof Error ? error.message : String(error));
    }
  }
}

/**
 * Register additional services
 */
async function registerServices(app: any): Promise<void> {
  const logger = getLogger();
  const isMemoryConstrained = process.env.MEMORY_CONSTRAINED_MODE === 'true';

  try {
    logger.info('[Main] Registering services...', { memoryConstrained: isMemoryConstrained });

    // Always register memory optimization service first
    const memoryOptimizationService = new MemoryOptimizationService(app);
    app.registerService(memoryOptimizationService, {
      autoStart: true,
      dependencies: [],
      priority: 1
    });

    // Core services (always needed)
    const economyService = new EconomyService(app);
    app.registerService(economyService, {
      autoStart: true,
      dependencies: ['DatabaseService'],
      priority: 2
    });

    if (isMemoryConstrained) {
      logger.info('[Main] Memory-constrained mode: Registering minimal services only');

      // In memory-constrained mode, only register essential services
      const roleService = new RoleService(app);
      app.registerService(roleService, {
        autoStart: false, // Lazy load when needed
        dependencies: ['DatabaseService', 'EconomyService'],
        priority: 10
      });

      // Register PollService in memory-constrained mode (essential for poll functionality)
      const pollService = new PollService(app);
      app.registerService(pollService, {
        autoStart: false, // Lazy load when needed
        dependencies: ['DatabaseService', 'EconomyService'],
        priority: 11
      });

      logger.info('[Main] Non-essential services will be lazy-loaded when needed');

    } else {
      // Normal mode - register all services immediately
      const roleService = new RoleService(app);
      app.registerService(roleService, {
        autoStart: true,
        dependencies: ['DatabaseService', 'EconomyService'],
        priority: 3
      });

      const tradeService = new TradeService(app);
      app.registerService(tradeService, {
        autoStart: true,
        dependencies: ['DatabaseService', 'EconomyService'],
        priority: 4
      });

      const disputeService = new DisputeService(app);
      app.registerService(disputeService, {
        autoStart: true,
        dependencies: ['DatabaseService', 'EconomyService', 'TradeService'],
        priority: 5
      });

      const tradeBackgroundService = new TradeBackgroundService(app);
      app.registerService(tradeBackgroundService, {
        autoStart: true,
        dependencies: ['DatabaseService', 'EconomyService', 'TradeService'],
        priority: 6
      });

      const electionService = new ElectionService(app);
      app.registerService(electionService, {
        autoStart: true,
        dependencies: ['DatabaseService', 'EconomyService'],
        priority: 7
      });

      // ElectionButtonHandler is already instantiated in InteractionCreateEventHandler
      // No additional service registration needed

      const salaryService = new SalaryService(app);
      app.registerService(salaryService, {
        autoStart: true,
        dependencies: ['DatabaseService', 'EconomyService'],
        priority: 8
      });

      const suggestionService = new SuggestionService(app);
      app.registerService(suggestionService, {
        autoStart: true,
        dependencies: ['DatabaseService'],
        priority: 9
      });

      const pollService = new PollService(app);
      app.registerService(pollService, {
        autoStart: true,
        dependencies: ['DatabaseService', 'EconomyService'],
        priority: 10
      });
    }

    logger.info('[Main] Services registered', {
      mode: isMemoryConstrained ? 'memory-constrained' : 'normal',
      immediateServices: isMemoryConstrained ? 2 : 8
    });
  } catch (error) {
    logger.error('[Main] Failed to register services', { error });
    throw error;
  }
}

/**
 * Load and register commands
 */
async function loadCommands(app: any): Promise<void> {
  const logger = getLogger();

  try {
    // Initialize commands collection if it doesn't exist
    if (!(app.client as any).commands) {
      (app.client as any).commands = new Map();
    }

    let loadedCommands = 0;

    // Load legacy commands (individual files)
    const commandsPath = path.join(__dirname, 'commands');
    const commandFiles = fs.readdirSync(commandsPath).filter(file =>
      (file.endsWith('.js') || file.endsWith('.ts')) &&
      !file.endsWith('.d.ts') &&
      !file.includes('index') &&
      !file.includes('Manager') &&
      !file.includes('Base')
    );

    // Skip files that are handled by new architecture
    // NOTE: Only skip commands that are confirmed to be working in new architecture
    const skipFiles = new Set([
      'balance.js', 'balance.ts',
      'pay.js', 'pay.ts',
      'give.js', 'give.ts',
      'enhancerole.js', 'enhancerole.ts',
      'updatenames.js', 'updatenames.ts'
    ]);

    for (const file of commandFiles) {
      if (skipFiles.has(file)) {
        logger.debug(`[Main] Skipping ${file} (handled by new architecture)`);
        continue;
      }

      try {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);

        if (command.data && command.execute) {
          (app.client as any).commands.set(command.data.name, command);
          loadedCommands++;
          logger.debug(`[Main] Loaded legacy command: ${command.data.name}`);
        } else {
          logger.warn(`[Main] Invalid command file: ${file}`);
        }
      } catch (error) {
        logger.error(`[Main] Failed to load command file: ${file}`, { error });
      }
    }

    // Load new architecture commands
    try {
      const { commandManager } = require('./commands/CommandManager');

      // Inject application context for dependency injection
      commandManager.setApplicationContext(app);

      const stats = await commandManager.loadCommands();

      const newCommands = commandManager.getDiscordCommands();
      for (const [name, command] of newCommands) {
        // Check if legacy command already exists
        if ((app.client as any).commands.has(name)) {
          logger.warn(`[Main] Skipping new architecture command '${name}' - legacy version already loaded`);
          continue;
        }

        (app.client as any).commands.set(name, command);
        loadedCommands++;
        logger.debug(`[Main] Loaded new architecture command: ${name}`);
      }

      logger.info(`[Main] CommandManager loaded ${stats.newArchitecture} new architecture commands`);
    } catch (error) {
      logger.error('[Main] Failed to load new architecture commands', { error });
    }

    logger.info(`[Main] Loaded ${loadedCommands} commands`);
  } catch (error) {
    logger.error('[Main] Failed to load commands', { error });
    throw error;
  }
}

/**
 * Initialize server configurations for existing guilds
 */
async function initializeServerConfigurations(app: any): Promise<void> {
  const logger = getLogger();

  try {
    logger.info('[Main] Initializing server configurations...');

    // Get all guild IDs from the Discord client
    const guildIds = app.client.guilds.cache.map((guild: any) => guild.id);

    if (guildIds.length > 0) {
      logger.info(`[Main] Found ${guildIds.length} guilds, ensuring configurations exist`);

      // Initialize default configurations for guilds that don't have them
      let initializedCount = 0;
      for (const guildId of guildIds) {
        try {
          // This will create a default configuration if none exists
          await ServerConfigurationService.getServerConfig(guildId);
          initializedCount++;
        } catch (error) {
          logger.warn(`[Main] Failed to initialize configuration for guild ${guildId}`, { error });
        }
      }

      logger.info(`[Main] Server configurations initialized for ${initializedCount}/${guildIds.length} guilds`);
    } else {
      logger.info('[Main] No guilds found, skipping server configuration initialization');
    }

  } catch (error) {
    logger.error('[Main] Failed to initialize server configurations', { error });
    // Don't throw error - this shouldn't prevent bot startup
  }
}

/**
 * Handle uncaught exceptions and rejections
 */
process.on('uncaughtException', (error) => {
  const logger = getLogger();
  logger.error('[Main] Uncaught exception', { error });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  const logger = getLogger();
  logger.error('[Main] Unhandled rejection', { reason, promise });
});

// Start the application
if (require.main === module) {
  main().catch((error) => {
    // Print any error directly to the console for maximum visibility
    // eslint-disable-next-line no-console
    console.error('[MAIN][Startup Error]', error);
    if (error && error.stack) {
      // eslint-disable-next-line no-console
      console.error('[MAIN][Startup Error Stack]', error.stack);
    }
    process.exit(1);
  });
}

export default main;
