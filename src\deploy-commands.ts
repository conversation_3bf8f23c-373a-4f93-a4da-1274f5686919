import { REST, Routes } from 'discord.js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

dotenv.config();

async function deployCommands() {
  const commands = [];

  try {
    console.log('🔄 Loading commands...');

    // Load legacy commands (individual files)
    const commandsPath = path.join(__dirname, 'commands');
    const commandFiles = fs.readdirSync(commandsPath).filter(file =>
      (file.endsWith('.js') || file.endsWith('.ts')) &&
      !file.includes('index') &&
      !file.includes('Manager') &&
      !file.includes('Base')
    );

    // Skip files that are handled by new architecture
    // NOTE: Only skip commands that are confirmed to be working in new architecture
    const skipFiles = new Set([
      'balance.ts', 'balance.js',
      'pay.ts', 'pay.js',
      'give.ts', 'give.js',
      'enhancerole.ts', 'enhancerole.js',
      'updatenames.ts', 'updatenames.js'
    ]);

    for (const file of commandFiles) {
      if (skipFiles.has(file)) {
        console.log(`⏭️  Skipping ${file} (handled by new architecture)`);
        continue;
      }

      try {
        const command = require(path.join(commandsPath, file));
        if (command.data) {
          commands.push(command.data.toJSON());
          console.log(`✅ Loaded legacy command: ${command.data.name}`);
        }
      } catch (error) {
        console.warn(`⚠️  Failed to load ${file}:`, error);
      }
    }

    // Load new architecture commands
    try {
      const { commandManager } = require('./commands/CommandManager');
      await commandManager.loadCommands();

      const existingCommandNames = new Set(commands.map(cmd => cmd.name));

      const newCommands = commandManager.getDiscordCommands();
      for (const [name, command] of newCommands) {
        if (command.data) {
          // Check for duplicates
          if (existingCommandNames.has(name)) {
            console.log(`⏭️  Skipping new architecture command '${name}' - legacy version already loaded`);
            continue;
          }

          commands.push(command.data.toJSON());
          console.log(`✅ Loaded new command: ${name}`);
        }
      }
    } catch (error) {
      console.warn('⚠️  Failed to load new architecture commands:', error);
    }

    console.log(`📊 Total commands to deploy: ${commands.length}`);

    if (commands.length === 0) {
      console.error('❌ No commands found to deploy!');
      return;
    }

    // Deploy to Discord
    const rest = new REST({ version: '10' }).setToken(process.env.BOT_TOKEN!);

    console.log('🚀 Started refreshing application (/) commands...');

    const data = await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID!),
      { body: commands }
    ) as any[];

    console.log(`✅ Successfully reloaded ${data.length} application (/) commands.`);

    // List deployed commands
    console.log('\n📋 Deployed commands:');
    for (const cmd of data) {
      console.log(`   • /${cmd.name} - ${cmd.description}`);
    }

  } catch (error) {
    console.error('❌ Error deploying commands:', error);
    process.exit(1);
  }
}

// Run deployment
deployCommands();
