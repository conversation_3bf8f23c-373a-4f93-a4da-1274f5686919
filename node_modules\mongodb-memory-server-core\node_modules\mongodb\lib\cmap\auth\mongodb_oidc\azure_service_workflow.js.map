{"version": 3, "file": "azure_service_workflow.js", "sourceRoot": "", "sources": ["../../../../src/cmap/auth/mongodb_oidc/azure_service_workflow.ts"], "names": [], "mappings": ";;;AAAA,0CAAiD;AACjD,0CAAyC;AAEzC,2DAAsD;AACtD,yDAAqD;AAErD,yCAAyC;AACzC,MAAM,cAAc,GAClB,8EAA8E,CAAC;AAEjF,6BAA6B;AAC7B,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC,CAAC;AAEtF,qCAAqC;AACrC,MAAM,qBAAqB,GACzB,wFAAwF,CAAC;AAE3F,uEAAuE;AACvE,MAAM,4BAA4B,GAChC,0FAA0F,CAAC;AAW7F;;;;GAIG;AACH,MAAa,oBAAqB,SAAQ,kCAAe;IAAzD;;QACE,UAAK,GAAG,IAAI,mCAAe,EAAE,CAAC;IAyBhC,CAAC;IAvBC;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAA8B;QAC3C,MAAM,aAAa,GAAG,WAAW,EAAE,mBAAmB,CAAC,cAAc,CAAC;QACtE,IAAI,CAAC,aAAa,EAAE;YAClB,MAAM,IAAI,uBAAe,CAAC,4BAA4B,CAAC,CAAC;SACzD;QACD,IAAI,KAAK,CAAC;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QACjD,IAAI,KAAK,EAAE,OAAO,EAAE,EAAE;YACpB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;SACrB;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YACtC,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACxD,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE;gBACpC,MAAM,IAAI,uBAAe,CAAC,qBAAqB,CAAC,CAAC;aAClD;YACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAC7C,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC;SAC/B;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA1BD,oDA0BC;AAED;;GAEG;AACH,KAAK,UAAU,iBAAiB,CAAC,aAAqB;IACpD,MAAM,GAAG,GAAG,GAAG,cAAc,aAAa,aAAa,EAAE,CAAC;IAC1D,MAAM,IAAI,GAAG,MAAM,IAAA,eAAO,EAAC,GAAG,EAAE;QAC9B,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,aAAa;KACvB,CAAC,CAAC;IACH,OAAO,IAAwB,CAAC;AAClC,CAAC;AAED;;;;GAIG;AACH,SAAS,qBAAqB,CAC5B,KAAc;IAEd,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAC7D,OAAO,cAAc,IAAI,KAAK,IAAI,YAAY,IAAI,KAAK,CAAC;AAC1D,CAAC"}