{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/util/getos/index.ts"], "names": [], "mappings": ";;;;AAAA,2BAA8B;AAC9B,0DAA0B;AAC1B,oCAA6D;AAE7D,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,eAAe,CAAC,CAAC;AAEnC,yDAAyD;AACzD,MAAM,QAAQ,GAAG;IACf,sFAAsF;IACtF,IAAI,EAAE,4CAA4C;IAClD,QAAQ,EAAE,4CAA4C;IACtD,OAAO,EAAE,0CAA0C;CACpD,CAAC;AAEF,0DAA0D;AAC1D,MAAM,OAAO,GAAG;IACd,IAAI,EAAE,wBAAwB;IAC9B,QAAQ,EAAE,iCAAiC;IAC3C,OAAO,EAAE,0CAA0C;IACnD,OAAO,EAAE,iCAAiC;CAC3C,CAAC;AAEF,+DAA+D;AAClD,QAAA,OAAO,GAAG,SAAS,CAAC;AAgBjC;;;GAGG;AACH,SAAgB,SAAS,CAAC,EAAS;IACjC,OAAO,EAAE,CAAC,EAAE,KAAK,OAAO,CAAC;AAC3B,CAAC;AAFD,8BAEC;AAED;;GAEG;AACH,IAAI,QAA2B,CAAC;AAEhC,uBAAuB;AAChB,KAAK,UAAU,KAAK;IACzB,IAAI,CAAC,QAAQ,EAAE;QACb,qDAAqD;QACrD,MAAM,MAAM,GAAG,IAAA,aAAQ,GAAE,CAAC;QAE1B,2BAA2B;QAC3B,IAAI,MAAM,KAAK,OAAO,EAAE;YACtB,QAAQ,GAAG,MAAM,mBAAmB,EAAE,CAAC;SACxC;aAAM;YACL,QAAQ,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC;SAC3B;KACF;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAdD,sBAcC;AAED,sDAAsD;AACtD,KAAK,UAAU,mBAAmB;IAChC,8BAA8B;IAC9B,uCAAuC;IACvC,sDAAsD;IACtD,sDAAsD;IACtD,uCAAuC;IAEvC,MAAM,WAAW,GAAG,MAAM,IAAA,sBAAc,EAAC,mCAAmC,EAAE,QAAQ,CAAC,CAAC;IAExF,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE;QAC1B,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAE9C,OAAO,WAAW,CAAC;KACpB;IAED,MAAM,YAAY,GAAG,MAAM,IAAA,sBAAc,EAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;IAEtE,IAAI,SAAS,CAAC,YAAY,CAAC,EAAE;QAC3B,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAE/C,OAAO,YAAY,CAAC;KACrB;IAED,MAAM,YAAY,GAAG,MAAM,IAAA,sBAAc,EAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;IAE1E,IAAI,SAAS,CAAC,YAAY,CAAC,EAAE;QAC3B,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAE/C,OAAO,YAAY,CAAC;KACrB;IAED,MAAM,aAAa,GAAG,MAAM,IAAA,sBAAc,EAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IAEzE,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE;QAC5B,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAEhD,OAAO,aAAa,CAAC;KACtB;IAED,OAAO,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;IAElF,qCAAqC;IACrC,OAAO;QACL,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,eAAO;QACb,OAAO,EAAE,EAAE;KACZ,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAgB,SAAS,CAAC,EAAuB;IAC/C,uBAAuB;IACvB,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,eAAO,EAAE;QAC7B,GAAG,CAAC,+CAA+C,EAAE,EAAE,CAAC,CAAC;KAC1D;IAED,OAAO,CAAC,IAAA,yBAAiB,EAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,eAAO,CAAC;AACvD,CAAC;AAPD,8BAOC;AAED;;GAEG;AACH,SAAgB,QAAQ,CAAC,KAAa;IACpC,OAAO;QACL,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,IAAI,eAAO;QACpE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE;QACjE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,IAAI,EAAE;KACtE,CAAC;AACJ,CAAC;AAPD,4BAOC;AAED;;GAEG;AACH,SAAgB,OAAO,CAAC,KAAa;IACnC,OAAO;QACL,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,IAAI,eAAO;QACnE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE;QAChE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,IAAI,EAAE;QACpE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;KAC1E,CAAC;AACJ,CAAC;AARD,0BAQC"}