{"name": "economy-bot", "version": "2.0.0", "main": "dist/main.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:trade": "jest tests/trade", "test:suggestion": "jest src/tests/suggestion", "test:pay": "jest tests/commands/economy/PayCommand", "test:economy": "jest tests/commands/economy", "validate:suggestion": "ts-node src/scripts/validateSuggestionSystem.ts", "verify:ttl": "ts-node src/scripts/verifyTTLSystem.ts", "build": "tsc", "build:watch": "tsc --watch", "build:prod": "ts-node scripts/build-production.ts", "build:prod:fast": "ts-node scripts/build-production.ts --skip-tests", "build:memory-optimized": "set NODE_ENV=production && set MEMORY_CONSTRAINED_MODE=true && tsc", "start": "node dist/main.js", "start:prod": "NODE_ENV=production DISCLOUD=true node dist/main.js", "start:memory-optimized": "node scripts/start-memory-optimized.js", "start:discloud": "NODE_ENV=production DISCLOUD=true MEMORY_CONSTRAINED_MODE=true MEMORY_MODE=ultra-minimal node scripts/start-memory-optimized.js", "start:discloud-essential": "NODE_ENV=production DISCLOUD=true MEMORY_CONSTRAINED_MODE=true MEMORY_MODE=essential node scripts/start-memory-optimized.js", "start:dev": "ts-node src/main.ts", "start:compiled": "node dist/main.js", "start:legacy": "node dist/index.js", "dev": "ts-node src/main.ts", "dev:legacy": "ts-node src/index.ts", "deploy-commands": "node dist/deploy-commands.js", "deploy-commands:prod": "NODE_ENV=production MEMORY_CONSTRAINED_MODE=true ts-node src/deploy-commands.ts", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "format": "prettier --write src/**/*.ts", "analyze-memory": "node -e \"require('./dist/utils/memoryAnalyzer.js').MemoryAnalyzer.getInstance().logMemoryAnalysis()\"", "migrate": "ts-node scripts/migrate.ts", "migrate:status": "npm run migrate status", "migrate:server-configs": "ts-node src/scripts/migrateServerConfigurations.ts", "test:server-config": "ts-node src/scripts/testServerConfiguration.ts", "migrate:up": "npm run migrate up", "migrate:down": "npm run migrate down", "migrate:create": "npm run migrate create", "migrate:multiserver": "ts-node scripts/migrate-to-multiserver.ts execute", "migrate:multiserver:dry-run": "ts-node scripts/migrate-to-multiserver.ts execute --dry-run", "migrate:multiserver:status": "ts-node scripts/migrate-to-multiserver.ts status", "migrate:data": "ts-node src/scripts/migrateToMultiServer.ts", "deploy": "ts-node scripts/deploy.ts", "deploy:dev": "npm run deploy deploy -e development", "deploy:staging": "npm run deploy deploy -e staging", "deploy:prod": "npm run deploy deploy -e production", "deploy:discloud": "ts-node scripts/deploy-discloud.ts", "deploy:discloud:fast": "ts-node scripts/deploy-discloud.ts --skip-tests", "deploy:validate": "npm run deploy validate", "deploy:rollback": "npm run deploy rollback", "test:integration": "ts-node scripts/test-command-integration.ts", "test:auto-deploy": "ts-node scripts/test-auto-deployment.ts", "test:auto-deploy:full": "TEST_BUILD=true TEST_DEPLOY=true ts-node scripts/test-auto-deployment.ts", "verify:deployment": "ts-node scripts/verify-deployment.ts", "verify:production": "ts-node scripts/verify-production-deployment.ts", "test:help-system": "node test-help-system.js", "test:multi-server": "node test-multi-server.js", "deploy:test": "node deploy-and-test.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@discordjs/rest": "^2.5.0", "discord-api-types": "^0.38.8", "discord.js": "^14.19.3", "dotenv": "^16.6.1", "mongodb": "^6.17.0", "mongoose": "^8.15.0", "node-cron": "^4.0.7", "winston": "^3.17.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^22.15.0", "commander": "^12.0.0", "jest": "^29.7.0", "mongodb-memory-server": "^9.1.6", "nodemon": "^3.1.10", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}