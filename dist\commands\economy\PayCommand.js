"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const economyService_1 = require("../../services/economyService");
const errorHandler_1 = require("../../utils/errorHandler");
const constants_1 = require("../../config/constants");
class PayCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'pay',
            description: 'Transfer coins to another user',
            category: BaseCommand_1.CommandCategory.ECONOMY,
            requiredFeatures: ['ECONOMY_SYSTEM'],
            cooldown: 5,
        });
    }
    customizeCommand(command) {
        command
            .addUserOption(option => option.setName('user')
            .setDescription('The user to send coins to')
            .setRequired(true))
            .addIntegerOption(option => option.setName('amount')
            .setDescription('Amount of coins to send')
            .setRequired(true)
            .setMinValue(constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT)
            .setMaxValue(constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT));
    }
    async executeCommand(context) {
        const { interaction } = context;
        const targetUser = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);
        const guildId = interaction.guild?.id;
        if (!guildId) {
            throw new Error('This command can only be used in a server');
        }
        this.validatePayment(interaction.user.id, targetUser.id, amount);
        try {
            const senderUser = await (0, economyService_1.ensureUser)(interaction.user.id, guildId);
            if (senderUser.balance < amount) {
                const formattedBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, senderUser.balance);
                const formattedAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, amount);
                throw new errorHandler_1.ValidationError(`Insufficient balance. You have ${formattedBalance} but need ${formattedAmount}.`);
            }
            await (0, economyService_1.ensureUser)(targetUser.id, guildId);
            await (0, economyService_1.adjustBalance)(interaction.user.id, guildId, -amount, 'pay', `Payment to ${targetUser.username} (${targetUser.id})`, interaction.client);
            await (0, economyService_1.adjustBalance)(targetUser.id, guildId, amount, 'pay', `Payment from ${interaction.user.username} (${interaction.user.id})`, interaction.client);
            const formattedAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, amount);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Payment Sent Successfully!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.TRANSFER} **Payment Completed**\n\n` +
                `${formattedAmount} has been sent to **${targetUser.displayName}**!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ACTIONS.SENDER} From`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ACTIONS.TARGET} To`,
                value: `**${targetUser.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
                value: formattedAmount,
                inline: true
            });
            const newBalance = senderUser.balance - amount;
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Payment completed: ${interaction.user.username} sent ${amount} PLC to ${targetUser.username}`);
            try {
                const recipientEmbed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Payment Received!');
                recipientEmbed.setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.COINS} You received ${formattedAmount} from **${interaction.user.displayName}**!`);
                await targetUser.send({ embeds: [recipientEmbed] });
            }
            catch (error) {
                this.logger.debug(`Failed to send payment notification DM to ${targetUser.username}`, { error });
            }
        }
        catch (error) {
            this.logger.error('Error executing pay command', {
                error,
                senderId: interaction.user.id,
                recipientId: targetUser.id,
                amount
            });
            throw error;
        }
    }
    validatePayment(senderId, recipientId, amount) {
        if (senderId === recipientId) {
            throw new errorHandler_1.ValidationError('You cannot send coins to yourself.');
        }
        if (amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be greater than 0.');
        }
        if (amount > constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT) {
            throw new errorHandler_1.ValidationError(`Amount cannot exceed ${constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT.toLocaleString()} coins.`);
        }
    }
}
exports.PayCommand = PayCommand;
