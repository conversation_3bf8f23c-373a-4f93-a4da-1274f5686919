{"version": 3, "sources": ["../src/collection.ts", "../src/index.ts"], "sourcesContent": ["/* eslint-disable no-param-reassign */\n/**\n * @internal\n */\nexport interface CollectionConstructor {\n\tnew (): Collection<unknown, unknown>;\n\tnew <K, V>(entries?: readonly (readonly [K, V])[] | null): Collection<K, V>;\n\tnew <K, V>(iterable: Iterable<readonly [K, V]>): Collection<K, V>;\n\treadonly prototype: Collection<unknown, unknown>;\n\treadonly [Symbol.species]: CollectionConstructor;\n}\n\n/**\n * Represents an immutable version of a collection\n */\nexport type ReadonlyCollection<K, V> = Omit<\n\tCollection<K, V>,\n\t'delete' | 'ensure' | 'forEach' | 'get' | 'reverse' | 'set' | 'sort' | 'sweep'\n> &\n\tReadonlyMap<K, V>;\n\n/**\n * Separate interface for the constructor so that emitted js does not have a constructor that overwrites itself\n *\n * @internal\n */\nexport interface Collection<K, V> extends Map<K, V> {\n\tconstructor: CollectionConstructor;\n}\n\n/**\n * A Map with additional utility methods. This is used throughout discord.js rather than Arrays for anything that has\n * an ID, for significantly improved performance and ease-of-use.\n *\n * @typeParam K - The key type this collection holds\n * @typeParam V - The value type this collection holds\n */\nexport class Collection<K, V> extends Map<K, V> {\n\t/**\n\t * Obtains the value of the given key if it exists, otherwise sets and returns the value provided by the default value generator.\n\t *\n\t * @param key - The key to get if it exists, or set otherwise\n\t * @param defaultValueGenerator - A function that generates the default value\n\t * @example\n\t * ```ts\n\t * collection.ensure(guildId, () => defaultGuildConfig);\n\t * ```\n\t */\n\tpublic ensure(key: K, defaultValueGenerator: (key: K, collection: this) => V): V {\n\t\tif (this.has(key)) return this.get(key)!;\n\t\tif (typeof defaultValueGenerator !== 'function') throw new TypeError(`${defaultValueGenerator} is not a function`);\n\t\tconst defaultValue = defaultValueGenerator(key, this);\n\t\tthis.set(key, defaultValue);\n\t\treturn defaultValue;\n\t}\n\n\t/**\n\t * Checks if all of the elements exist in the collection.\n\t *\n\t * @param keys - The keys of the elements to check for\n\t * @returns `true` if all of the elements exist, `false` if at least one does not exist.\n\t */\n\tpublic hasAll(...keys: K[]) {\n\t\treturn keys.every((key) => super.has(key));\n\t}\n\n\t/**\n\t * Checks if any of the elements exist in the collection.\n\t *\n\t * @param keys - The keys of the elements to check for\n\t * @returns `true` if any of the elements exist, `false` if none exist.\n\t */\n\tpublic hasAny(...keys: K[]) {\n\t\treturn keys.some((key) => super.has(key));\n\t}\n\n\t/**\n\t * Obtains the first value(s) in this collection.\n\t *\n\t * @param amount - Amount of values to obtain from the beginning\n\t * @returns A single value if no amount is provided or an array of values, starting from the end if amount is negative\n\t */\n\tpublic first(): V | undefined;\n\tpublic first(amount: number): V[];\n\tpublic first(amount?: number): V | V[] | undefined {\n\t\tif (amount === undefined) return this.values().next().value;\n\t\tif (amount < 0) return this.last(amount * -1);\n\t\tamount = Math.min(this.size, amount);\n\t\tconst iter = this.values();\n\t\treturn Array.from({ length: amount }, (): V => iter.next().value);\n\t}\n\n\t/**\n\t * Obtains the first key(s) in this collection.\n\t *\n\t * @param amount - Amount of keys to obtain from the beginning\n\t * @returns A single key if no amount is provided or an array of keys, starting from the end if\n\t * amount is negative\n\t */\n\tpublic firstKey(): K | undefined;\n\tpublic firstKey(amount: number): K[];\n\tpublic firstKey(amount?: number): K | K[] | undefined {\n\t\tif (amount === undefined) return this.keys().next().value;\n\t\tif (amount < 0) return this.lastKey(amount * -1);\n\t\tamount = Math.min(this.size, amount);\n\t\tconst iter = this.keys();\n\t\treturn Array.from({ length: amount }, (): K => iter.next().value);\n\t}\n\n\t/**\n\t * Obtains the last value(s) in this collection.\n\t *\n\t * @param amount - Amount of values to obtain from the end\n\t * @returns A single value if no amount is provided or an array of values, starting from the start if\n\t * amount is negative\n\t */\n\tpublic last(): V | undefined;\n\tpublic last(amount: number): V[];\n\tpublic last(amount?: number): V | V[] | undefined {\n\t\tconst arr = [...this.values()];\n\t\tif (amount === undefined) return arr[arr.length - 1];\n\t\tif (amount < 0) return this.first(amount * -1);\n\t\tif (!amount) return [];\n\t\treturn arr.slice(-amount);\n\t}\n\n\t/**\n\t * Obtains the last key(s) in this collection.\n\t *\n\t * @param amount - Amount of keys to obtain from the end\n\t * @returns A single key if no amount is provided or an array of keys, starting from the start if\n\t * amount is negative\n\t */\n\tpublic lastKey(): K | undefined;\n\tpublic lastKey(amount: number): K[];\n\tpublic lastKey(amount?: number): K | K[] | undefined {\n\t\tconst arr = [...this.keys()];\n\t\tif (amount === undefined) return arr[arr.length - 1];\n\t\tif (amount < 0) return this.firstKey(amount * -1);\n\t\tif (!amount) return [];\n\t\treturn arr.slice(-amount);\n\t}\n\n\t/**\n\t * Identical to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/at | Array.at()}.\n\t * Returns the item at a given index, allowing for positive and negative integers.\n\t * Negative integers count back from the last item in the collection.\n\t *\n\t * @param index - The index of the element to obtain\n\t */\n\tpublic at(index: number) {\n\t\tindex = Math.floor(index);\n\t\tconst arr = [...this.values()];\n\t\treturn arr.at(index);\n\t}\n\n\t/**\n\t * Identical to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/at | Array.at()}.\n\t * Returns the key at a given index, allowing for positive and negative integers.\n\t * Negative integers count back from the last item in the collection.\n\t *\n\t * @param index - The index of the key to obtain\n\t */\n\tpublic keyAt(index: number) {\n\t\tindex = Math.floor(index);\n\t\tconst arr = [...this.keys()];\n\t\treturn arr.at(index);\n\t}\n\n\t/**\n\t * Obtains unique random value(s) from this collection.\n\t *\n\t * @param amount - Amount of values to obtain randomly\n\t * @returns A single value if no amount is provided or an array of values\n\t */\n\tpublic random(): V | undefined;\n\tpublic random(amount: number): V[];\n\tpublic random(amount?: number): V | V[] | undefined {\n\t\tconst arr = [...this.values()];\n\t\tif (amount === undefined) return arr[Math.floor(Math.random() * arr.length)];\n\t\tif (!arr.length || !amount) return [];\n\t\treturn Array.from(\n\t\t\t{ length: Math.min(amount, arr.length) },\n\t\t\t(): V => arr.splice(Math.floor(Math.random() * arr.length), 1)[0]!,\n\t\t);\n\t}\n\n\t/**\n\t * Obtains unique random key(s) from this collection.\n\t *\n\t * @param amount - Amount of keys to obtain randomly\n\t * @returns A single key if no amount is provided or an array\n\t */\n\tpublic randomKey(): K | undefined;\n\tpublic randomKey(amount: number): K[];\n\tpublic randomKey(amount?: number): K | K[] | undefined {\n\t\tconst arr = [...this.keys()];\n\t\tif (amount === undefined) return arr[Math.floor(Math.random() * arr.length)];\n\t\tif (!arr.length || !amount) return [];\n\t\treturn Array.from(\n\t\t\t{ length: Math.min(amount, arr.length) },\n\t\t\t(): K => arr.splice(Math.floor(Math.random() * arr.length), 1)[0]!,\n\t\t);\n\t}\n\n\t/**\n\t * Identical to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reverse | Array.reverse()}\n\t * but returns a Collection instead of an Array.\n\t */\n\tpublic reverse() {\n\t\tconst entries = [...this.entries()].reverse();\n\t\tthis.clear();\n\t\tfor (const [key, value] of entries) this.set(key, value);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Searches for a single item where the given function returns a truthy value. This behaves like\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/find | Array.find()}.\n\t * All collections used in Discord.js are mapped using their `id` property, and if you want to find by id you\n\t * should use the `get` method. See\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/get | MDN} for details.\n\t *\n\t * @param fn - The function to test with (should return boolean)\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection.find(user => user.username === 'Bob');\n\t * ```\n\t */\n\tpublic find<V2 extends V>(fn: (value: V, key: K, collection: this) => value is V2): V2 | undefined;\n\tpublic find(fn: (value: V, key: K, collection: this) => unknown): V | undefined;\n\tpublic find<This, V2 extends V>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => value is V2,\n\t\tthisArg: This,\n\t): V2 | undefined;\n\tpublic find<This>(fn: (this: This, value: V, key: K, collection: this) => unknown, thisArg: This): V | undefined;\n\tpublic find(fn: (value: V, key: K, collection: this) => unknown, thisArg?: unknown): V | undefined {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) return val;\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * Searches for the key of a single item where the given function returns a truthy value. This behaves like\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/findIndex | Array.findIndex()},\n\t * but returns the key rather than the positional index.\n\t *\n\t * @param fn - The function to test with (should return boolean)\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection.findKey(user => user.username === 'Bob');\n\t * ```\n\t */\n\tpublic findKey<K2 extends K>(fn: (value: V, key: K, collection: this) => key is K2): K2 | undefined;\n\tpublic findKey(fn: (value: V, key: K, collection: this) => unknown): K | undefined;\n\tpublic findKey<This, K2 extends K>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => key is K2,\n\t\tthisArg: This,\n\t): K2 | undefined;\n\tpublic findKey<This>(fn: (this: This, value: V, key: K, collection: this) => unknown, thisArg: This): K | undefined;\n\tpublic findKey(fn: (value: V, key: K, collection: this) => unknown, thisArg?: unknown): K | undefined {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) return key;\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\t/**\n\t * Removes items that satisfy the provided filter function.\n\t *\n\t * @param fn - Function used to test (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @returns The number of removed entries\n\t */\n\tpublic sweep(fn: (value: V, key: K, collection: this) => unknown): number;\n\tpublic sweep<T>(fn: (this: T, value: V, key: K, collection: this) => unknown, thisArg: T): number;\n\tpublic sweep(fn: (value: V, key: K, collection: this) => unknown, thisArg?: unknown): number {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst previousSize = this.size;\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) this.delete(key);\n\t\t}\n\n\t\treturn previousSize - this.size;\n\t}\n\n\t/**\n\t * Identical to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/filter | Array.filter()},\n\t * but returns a Collection instead of an Array.\n\t *\n\t * @param fn - The function to test with (should return boolean)\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection.filter(user => user.username === 'Bob');\n\t * ```\n\t */\n\tpublic filter<K2 extends K>(fn: (value: V, key: K, collection: this) => key is K2): Collection<K2, V>;\n\tpublic filter<V2 extends V>(fn: (value: V, key: K, collection: this) => value is V2): Collection<K, V2>;\n\tpublic filter(fn: (value: V, key: K, collection: this) => unknown): Collection<K, V>;\n\tpublic filter<This, K2 extends K>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => key is K2,\n\t\tthisArg: This,\n\t): Collection<K2, V>;\n\tpublic filter<This, V2 extends V>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => value is V2,\n\t\tthisArg: This,\n\t): Collection<K, V2>;\n\tpublic filter<This>(fn: (this: This, value: V, key: K, collection: this) => unknown, thisArg: This): Collection<K, V>;\n\tpublic filter(fn: (value: V, key: K, collection: this) => unknown, thisArg?: unknown): Collection<K, V> {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst results = new this.constructor[Symbol.species]<K, V>();\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) results.set(key, val);\n\t\t}\n\n\t\treturn results;\n\t}\n\n\t/**\n\t * Partitions the collection into two collections where the first collection\n\t * contains the items that passed and the second contains the items that failed.\n\t *\n\t * @param fn - Function used to test (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * const [big, small] = collection.partition(guild => guild.memberCount > 250);\n\t * ```\n\t */\n\tpublic partition<K2 extends K>(\n\t\tfn: (value: V, key: K, collection: this) => key is K2,\n\t): [Collection<K2, V>, Collection<Exclude<K, K2>, V>];\n\tpublic partition<V2 extends V>(\n\t\tfn: (value: V, key: K, collection: this) => value is V2,\n\t): [Collection<K, V2>, Collection<K, Exclude<V, V2>>];\n\tpublic partition(fn: (value: V, key: K, collection: this) => unknown): [Collection<K, V>, Collection<K, V>];\n\tpublic partition<This, K2 extends K>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => key is K2,\n\t\tthisArg: This,\n\t): [Collection<K2, V>, Collection<Exclude<K, K2>, V>];\n\tpublic partition<This, V2 extends V>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => value is V2,\n\t\tthisArg: This,\n\t): [Collection<K, V2>, Collection<K, Exclude<V, V2>>];\n\tpublic partition<This>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => unknown,\n\t\tthisArg: This,\n\t): [Collection<K, V>, Collection<K, V>];\n\tpublic partition(\n\t\tfn: (value: V, key: K, collection: this) => unknown,\n\t\tthisArg?: unknown,\n\t): [Collection<K, V>, Collection<K, V>] {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst results: [Collection<K, V>, Collection<K, V>] = [\n\t\t\tnew this.constructor[Symbol.species]<K, V>(),\n\t\t\tnew this.constructor[Symbol.species]<K, V>(),\n\t\t];\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) {\n\t\t\t\tresults[0].set(key, val);\n\t\t\t} else {\n\t\t\t\tresults[1].set(key, val);\n\t\t\t}\n\t\t}\n\n\t\treturn results;\n\t}\n\n\t/**\n\t * Maps each item into a Collection, then joins the results into a single Collection. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/flatMap | Array.flatMap()}.\n\t *\n\t * @param fn - Function that produces a new Collection\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection.flatMap(guild => guild.members.cache);\n\t * ```\n\t */\n\tpublic flatMap<T>(fn: (value: V, key: K, collection: this) => Collection<K, T>): Collection<K, T>;\n\tpublic flatMap<T, This>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => Collection<K, T>,\n\t\tthisArg: This,\n\t): Collection<K, T>;\n\tpublic flatMap<T>(fn: (value: V, key: K, collection: this) => Collection<K, T>, thisArg?: unknown): Collection<K, T> {\n\t\t// eslint-disable-next-line unicorn/no-array-method-this-argument\n\t\tconst collections = this.map(fn, thisArg);\n\t\treturn new this.constructor[Symbol.species]<K, T>().concat(...collections);\n\t}\n\n\t/**\n\t * Maps each item to another value into an array. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map | Array.map()}.\n\t *\n\t * @param fn - Function that produces an element of the new array, taking three arguments\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection.map(user => user.tag);\n\t * ```\n\t */\n\tpublic map<T>(fn: (value: V, key: K, collection: this) => T): T[];\n\tpublic map<This, T>(fn: (this: This, value: V, key: K, collection: this) => T, thisArg: This): T[];\n\tpublic map<T>(fn: (value: V, key: K, collection: this) => T, thisArg?: unknown): T[] {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst iter = this.entries();\n\t\treturn Array.from({ length: this.size }, (): T => {\n\t\t\tconst [key, value] = iter.next().value;\n\t\t\treturn fn(value, key, this);\n\t\t});\n\t}\n\n\t/**\n\t * Maps each item to another value into a collection. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map | Array.map()}.\n\t *\n\t * @param fn - Function that produces an element of the new collection, taking three arguments\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection.mapValues(user => user.tag);\n\t * ```\n\t */\n\tpublic mapValues<T>(fn: (value: V, key: K, collection: this) => T): Collection<K, T>;\n\tpublic mapValues<This, T>(fn: (this: This, value: V, key: K, collection: this) => T, thisArg: This): Collection<K, T>;\n\tpublic mapValues<T>(fn: (value: V, key: K, collection: this) => T, thisArg?: unknown): Collection<K, T> {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tconst coll = new this.constructor[Symbol.species]<K, T>();\n\t\tfor (const [key, val] of this) coll.set(key, fn(val, key, this));\n\t\treturn coll;\n\t}\n\n\t/**\n\t * Checks if there exists an item that passes a test. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/some | Array.some()}.\n\t *\n\t * @param fn - Function used to test (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection.some(user => user.discriminator === '0000');\n\t * ```\n\t */\n\tpublic some(fn: (value: V, key: K, collection: this) => unknown): boolean;\n\tpublic some<T>(fn: (this: T, value: V, key: K, collection: this) => unknown, thisArg: T): boolean;\n\tpublic some(fn: (value: V, key: K, collection: this) => unknown, thisArg?: unknown): boolean {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (fn(val, key, this)) return true;\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t * Checks if all items passes a test. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/every | Array.every()}.\n\t *\n\t * @param fn - Function used to test (should return a boolean)\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection.every(user => !user.bot);\n\t * ```\n\t */\n\tpublic every<K2 extends K>(fn: (value: V, key: K, collection: this) => key is K2): this is Collection<K2, V>;\n\tpublic every<V2 extends V>(fn: (value: V, key: K, collection: this) => value is V2): this is Collection<K, V2>;\n\tpublic every(fn: (value: V, key: K, collection: this) => unknown): boolean;\n\tpublic every<This, K2 extends K>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => key is K2,\n\t\tthisArg: This,\n\t): this is Collection<K2, V>;\n\tpublic every<This, V2 extends V>(\n\t\tfn: (this: This, value: V, key: K, collection: this) => value is V2,\n\t\tthisArg: This,\n\t): this is Collection<K, V2>;\n\tpublic every<This>(fn: (this: This, value: V, key: K, collection: this) => unknown, thisArg: This): boolean;\n\tpublic every(fn: (value: V, key: K, collection: this) => unknown, thisArg?: unknown): boolean {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfor (const [key, val] of this) {\n\t\t\tif (!fn(val, key, this)) return false;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Applies a function to produce a single value. Identical in behavior to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce | Array.reduce()}.\n\t *\n\t * @param fn - Function used to reduce, taking four arguments; `accumulator`, `currentValue`, `currentKey`,\n\t * and `collection`\n\t * @param initialValue - Starting value for the accumulator\n\t * @example\n\t * ```ts\n\t * collection.reduce((acc, guild) => acc + guild.memberCount, 0);\n\t * ```\n\t */\n\tpublic reduce<T = V>(fn: (accumulator: T, value: V, key: K, collection: this) => T, initialValue?: T): T {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tlet accumulator!: T;\n\n\t\tconst iterator = this.entries();\n\t\tif (initialValue === undefined) {\n\t\t\tif (this.size === 0) throw new TypeError('Reduce of empty collection with no initial value');\n\t\t\taccumulator = iterator.next().value[1];\n\t\t} else {\n\t\t\taccumulator = initialValue;\n\t\t}\n\n\t\tfor (const [key, value] of iterator) {\n\t\t\taccumulator = fn(accumulator, value, key, this);\n\t\t}\n\n\t\treturn accumulator;\n\t}\n\n\t/**\n\t * Identical to\n\t * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map/forEach | Map.forEach()},\n\t * but returns the collection instead of undefined.\n\t *\n\t * @param fn - Function to execute for each element\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection\n\t *  .each(user => console.log(user.username))\n\t *  .filter(user => user.bot)\n\t *  .each(user => console.log(user.username));\n\t * ```\n\t */\n\tpublic each(fn: (value: V, key: K, collection: this) => void): this;\n\tpublic each<T>(fn: (this: T, value: V, key: K, collection: this) => void, thisArg: T): this;\n\tpublic each(fn: (value: V, key: K, collection: this) => void, thisArg?: unknown): this {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\n\t\tfor (const [key, value] of this) {\n\t\t\tfn(value, key, this);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Runs a function on the collection and returns the collection.\n\t *\n\t * @param fn - Function to execute\n\t * @param thisArg - Value to use as `this` when executing function\n\t * @example\n\t * ```ts\n\t * collection\n\t *  .tap(coll => console.log(coll.size))\n\t *  .filter(user => user.bot)\n\t *  .tap(coll => console.log(coll.size))\n\t * ```\n\t */\n\tpublic tap(fn: (collection: this) => void): this;\n\tpublic tap<T>(fn: (this: T, collection: this) => void, thisArg: T): this;\n\tpublic tap(fn: (collection: this) => void, thisArg?: unknown): this {\n\t\tif (typeof fn !== 'function') throw new TypeError(`${fn} is not a function`);\n\t\tif (thisArg !== undefined) fn = fn.bind(thisArg);\n\t\tfn(this);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Creates an identical shallow copy of this collection.\n\t *\n\t * @example\n\t * ```ts\n\t * const newColl = someColl.clone();\n\t * ```\n\t */\n\tpublic clone(): Collection<K, V> {\n\t\treturn new this.constructor[Symbol.species](this);\n\t}\n\n\t/**\n\t * Combines this collection with others into a new collection. None of the source collections are modified.\n\t *\n\t * @param collections - Collections to merge\n\t * @example\n\t * ```ts\n\t * const newColl = someColl.concat(someOtherColl, anotherColl, ohBoyAColl);\n\t * ```\n\t */\n\tpublic concat(...collections: ReadonlyCollection<K, V>[]) {\n\t\tconst newColl = this.clone();\n\t\tfor (const coll of collections) {\n\t\t\tfor (const [key, val] of coll) newColl.set(key, val);\n\t\t}\n\n\t\treturn newColl;\n\t}\n\n\t/**\n\t * Checks if this collection shares identical items with another.\n\t * This is different to checking for equality using equal-signs, because\n\t * the collections may be different objects, but contain the same data.\n\t *\n\t * @param collection - Collection to compare with\n\t * @returns Whether the collections have identical contents\n\t */\n\tpublic equals(collection: ReadonlyCollection<K, V>) {\n\t\tif (!collection) return false; // runtime check\n\t\tif (this === collection) return true;\n\t\tif (this.size !== collection.size) return false;\n\t\tfor (const [key, value] of this) {\n\t\t\tif (!collection.has(key) || value !== collection.get(key)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * The sort method sorts the items of a collection in place and returns it.\n\t * The sort is not necessarily stable in Node 10 or older.\n\t * The default sort order is according to string Unicode code points.\n\t *\n\t * @param compareFunction - Specifies a function that defines the sort order.\n\t * If omitted, the collection is sorted according to each character's Unicode code point value, according to the string conversion of each element.\n\t * @example\n\t * ```ts\n\t * collection.sort((userA, userB) => userA.createdTimestamp - userB.createdTimestamp);\n\t * ```\n\t */\n\tpublic sort(compareFunction: Comparator<K, V> = Collection.defaultSort) {\n\t\tconst entries = [...this.entries()];\n\t\tentries.sort((a, b): number => compareFunction(a[1], b[1], a[0], b[0]));\n\n\t\t// Perform clean-up\n\t\tsuper.clear();\n\n\t\t// Set the new entries\n\t\tfor (const [key, value] of entries) {\n\t\t\tsuper.set(key, value);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * The intersect method returns a new structure containing items where the keys and values are present in both original structures.\n\t *\n\t * @param other - The other Collection to filter against\n\t */\n\tpublic intersect<T>(other: ReadonlyCollection<K, T>): Collection<K, T> {\n\t\tconst coll = new this.constructor[Symbol.species]<K, T>();\n\t\tfor (const [key, value] of other) {\n\t\t\tif (this.has(key) && Object.is(value, this.get(key))) {\n\t\t\t\tcoll.set(key, value);\n\t\t\t}\n\t\t}\n\n\t\treturn coll;\n\t}\n\n\t/**\n\t * The subtract method returns a new structure containing items where the keys and values of the original structure are not present in the other.\n\t *\n\t * @param other - The other Collection to filter against\n\t */\n\tpublic subtract<T>(other: ReadonlyCollection<K, T>): Collection<K, V> {\n\t\tconst coll = new this.constructor[Symbol.species]<K, V>();\n\t\tfor (const [key, value] of this) {\n\t\t\tif (!other.has(key) || !Object.is(value, other.get(key))) {\n\t\t\t\tcoll.set(key, value);\n\t\t\t}\n\t\t}\n\n\t\treturn coll;\n\t}\n\n\t/**\n\t * The difference method returns a new structure containing items where the key is present in one of the original structures but not the other.\n\t *\n\t * @param other - The other Collection to filter against\n\t */\n\tpublic difference<T>(other: ReadonlyCollection<K, T>): Collection<K, T | V> {\n\t\tconst coll = new this.constructor[Symbol.species]<K, T | V>();\n\t\tfor (const [key, value] of other) {\n\t\t\tif (!this.has(key)) coll.set(key, value);\n\t\t}\n\n\t\tfor (const [key, value] of this) {\n\t\t\tif (!other.has(key)) coll.set(key, value);\n\t\t}\n\n\t\treturn coll;\n\t}\n\n\t/**\n\t * Merges two Collections together into a new Collection.\n\t *\n\t * @param other - The other Collection to merge with\n\t * @param whenInSelf - Function getting the result if the entry only exists in this Collection\n\t * @param whenInOther - Function getting the result if the entry only exists in the other Collection\n\t * @param whenInBoth - Function getting the result if the entry exists in both Collections\n\t * @example\n\t * ```ts\n\t * // Sums up the entries in two collections.\n\t * coll.merge(\n\t *  other,\n\t *  x => ({ keep: true, value: x }),\n\t *  y => ({ keep: true, value: y }),\n\t *  (x, y) => ({ keep: true, value: x + y }),\n\t * );\n\t * ```\n\t * @example\n\t * ```ts\n\t * // Intersects two collections in a left-biased manner.\n\t * coll.merge(\n\t *  other,\n\t *  x => ({ keep: false }),\n\t *  y => ({ keep: false }),\n\t *  (x, _) => ({ keep: true, value: x }),\n\t * );\n\t * ```\n\t */\n\tpublic merge<T, R>(\n\t\tother: ReadonlyCollection<K, T>,\n\t\twhenInSelf: (value: V, key: K) => Keep<R>,\n\t\twhenInOther: (valueOther: T, key: K) => Keep<R>,\n\t\twhenInBoth: (value: V, valueOther: T, key: K) => Keep<R>,\n\t): Collection<K, R> {\n\t\tconst coll = new this.constructor[Symbol.species]<K, R>();\n\t\tconst keys = new Set([...this.keys(), ...other.keys()]);\n\n\t\tfor (const key of keys) {\n\t\t\tconst hasInSelf = this.has(key);\n\t\t\tconst hasInOther = other.has(key);\n\n\t\t\tif (hasInSelf && hasInOther) {\n\t\t\t\tconst result = whenInBoth(this.get(key)!, other.get(key)!, key);\n\t\t\t\tif (result.keep) coll.set(key, result.value);\n\t\t\t} else if (hasInSelf) {\n\t\t\t\tconst result = whenInSelf(this.get(key)!, key);\n\t\t\t\tif (result.keep) coll.set(key, result.value);\n\t\t\t} else if (hasInOther) {\n\t\t\t\tconst result = whenInOther(other.get(key)!, key);\n\t\t\t\tif (result.keep) coll.set(key, result.value);\n\t\t\t}\n\t\t}\n\n\t\treturn coll;\n\t}\n\n\t/**\n\t * The sorted method sorts the items of a collection and returns it.\n\t * The sort is not necessarily stable in Node 10 or older.\n\t * The default sort order is according to string Unicode code points.\n\t *\n\t * @param compareFunction - Specifies a function that defines the sort order.\n\t * If omitted, the collection is sorted according to each character's Unicode code point value,\n\t * according to the string conversion of each element.\n\t * @example\n\t * ```ts\n\t * collection.sorted((userA, userB) => userA.createdTimestamp - userB.createdTimestamp);\n\t * ```\n\t */\n\tpublic sorted(compareFunction: Comparator<K, V> = Collection.defaultSort) {\n\t\treturn new this.constructor[Symbol.species](this).sort((av, bv, ak, bk) => compareFunction(av, bv, ak, bk));\n\t}\n\n\tpublic toJSON() {\n\t\t// toJSON is called recursively by JSON.stringify.\n\t\treturn [...this.values()];\n\t}\n\n\tprivate static defaultSort<V>(firstValue: V, secondValue: V): number {\n\t\treturn Number(firstValue > secondValue) || Number(firstValue === secondValue) - 1;\n\t}\n\n\t/**\n\t * Creates a Collection from a list of entries.\n\t *\n\t * @param entries - The list of entries\n\t * @param combine - Function to combine an existing entry with a new one\n\t * @example\n\t * ```ts\n\t * Collection.combineEntries([[\"a\", 1], [\"b\", 2], [\"a\", 2]], (x, y) => x + y);\n\t * // returns Collection { \"a\" => 3, \"b\" => 2 }\n\t * ```\n\t */\n\tpublic static combineEntries<K, V>(\n\t\tentries: Iterable<[K, V]>,\n\t\tcombine: (firstValue: V, secondValue: V, key: K) => V,\n\t): Collection<K, V> {\n\t\tconst coll = new Collection<K, V>();\n\t\tfor (const [key, value] of entries) {\n\t\t\tif (coll.has(key)) {\n\t\t\t\tcoll.set(key, combine(coll.get(key)!, value, key));\n\t\t\t} else {\n\t\t\t\tcoll.set(key, value);\n\t\t\t}\n\t\t}\n\n\t\treturn coll;\n\t}\n}\n\n/**\n * @internal\n */\nexport type Keep<V> = { keep: false } | { keep: true; value: V };\n\n/**\n * @internal\n */\nexport type Comparator<K, V> = (firstValue: V, secondValue: V, firstKey: K, secondKey: K) => number;\n", "export * from './collection.js';\n\n/**\n * The {@link https://github.com/discordjs/discord.js/blob/main/packages/collection/#readme | @discordjs/collection} version\n * that you are currently using.\n */\n// This needs to explicitly be `string` so it is not typed as a \"const string\" that gets injected by esbuild\nexport const version = '1.5.3' as string;\n"], "mappings": ";;;;AAqCO,IAAM,aAAN,MAAM,oBAAyB,IAAU;AAAA,EArChD,OAqCgD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWxC,OAAO,KAAQ,uBAA2D;AAChF,QAAI,KAAK,IAAI,GAAG;AAAG,aAAO,KAAK,IAAI,GAAG;AACtC,QAAI,OAAO,0BAA0B;AAAY,YAAM,IAAI,UAAU,GAAG,qBAAqB,oBAAoB;AACjH,UAAM,eAAe,sBAAsB,KAAK,IAAI;AACpD,SAAK,IAAI,KAAK,YAAY;AAC1B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,UAAU,MAAW;AAC3B,WAAO,KAAK,MAAM,CAAC,QAAQ,MAAM,IAAI,GAAG,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQO,UAAU,MAAW;AAC3B,WAAO,KAAK,KAAK,CAAC,QAAQ,MAAM,IAAI,GAAG,CAAC;AAAA,EACzC;AAAA,EAUO,MAAM,QAAsC;AAClD,QAAI,WAAW;AAAW,aAAO,KAAK,OAAO,EAAE,KAAK,EAAE;AACtD,QAAI,SAAS;AAAG,aAAO,KAAK,KAAK,SAAS,EAAE;AAC5C,aAAS,KAAK,IAAI,KAAK,MAAM,MAAM;AACnC,UAAM,OAAO,KAAK,OAAO;AACzB,WAAO,MAAM,KAAK,EAAE,QAAQ,OAAO,GAAG,MAAS,KAAK,KAAK,EAAE,KAAK;AAAA,EACjE;AAAA,EAWO,SAAS,QAAsC;AACrD,QAAI,WAAW;AAAW,aAAO,KAAK,KAAK,EAAE,KAAK,EAAE;AACpD,QAAI,SAAS;AAAG,aAAO,KAAK,QAAQ,SAAS,EAAE;AAC/C,aAAS,KAAK,IAAI,KAAK,MAAM,MAAM;AACnC,UAAM,OAAO,KAAK,KAAK;AACvB,WAAO,MAAM,KAAK,EAAE,QAAQ,OAAO,GAAG,MAAS,KAAK,KAAK,EAAE,KAAK;AAAA,EACjE;AAAA,EAWO,KAAK,QAAsC;AACjD,UAAM,MAAM,CAAC,GAAG,KAAK,OAAO,CAAC;AAC7B,QAAI,WAAW;AAAW,aAAO,IAAI,IAAI,SAAS,CAAC;AACnD,QAAI,SAAS;AAAG,aAAO,KAAK,MAAM,SAAS,EAAE;AAC7C,QAAI,CAAC;AAAQ,aAAO,CAAC;AACrB,WAAO,IAAI,MAAM,CAAC,MAAM;AAAA,EACzB;AAAA,EAWO,QAAQ,QAAsC;AACpD,UAAM,MAAM,CAAC,GAAG,KAAK,KAAK,CAAC;AAC3B,QAAI,WAAW;AAAW,aAAO,IAAI,IAAI,SAAS,CAAC;AACnD,QAAI,SAAS;AAAG,aAAO,KAAK,SAAS,SAAS,EAAE;AAChD,QAAI,CAAC;AAAQ,aAAO,CAAC;AACrB,WAAO,IAAI,MAAM,CAAC,MAAM;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,GAAG,OAAe;AACxB,YAAQ,KAAK,MAAM,KAAK;AACxB,UAAM,MAAM,CAAC,GAAG,KAAK,OAAO,CAAC;AAC7B,WAAO,IAAI,GAAG,KAAK;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASO,MAAM,OAAe;AAC3B,YAAQ,KAAK,MAAM,KAAK;AACxB,UAAM,MAAM,CAAC,GAAG,KAAK,KAAK,CAAC;AAC3B,WAAO,IAAI,GAAG,KAAK;AAAA,EACpB;AAAA,EAUO,OAAO,QAAsC;AACnD,UAAM,MAAM,CAAC,GAAG,KAAK,OAAO,CAAC;AAC7B,QAAI,WAAW;AAAW,aAAO,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,CAAC;AAC3E,QAAI,CAAC,IAAI,UAAU,CAAC;AAAQ,aAAO,CAAC;AACpC,WAAO,MAAM;AAAA,MACZ,EAAE,QAAQ,KAAK,IAAI,QAAQ,IAAI,MAAM,EAAE;AAAA,MACvC,MAAS,IAAI,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;AAAA,IACjE;AAAA,EACD;AAAA,EAUO,UAAU,QAAsC;AACtD,UAAM,MAAM,CAAC,GAAG,KAAK,KAAK,CAAC;AAC3B,QAAI,WAAW;AAAW,aAAO,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,CAAC;AAC3E,QAAI,CAAC,IAAI,UAAU,CAAC;AAAQ,aAAO,CAAC;AACpC,WAAO,MAAM;AAAA,MACZ,EAAE,QAAQ,KAAK,IAAI,QAAQ,IAAI,MAAM,EAAE;AAAA,MACvC,MAAS,IAAI,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;AAAA,IACjE;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,UAAU;AAChB,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC,EAAE,QAAQ;AAC5C,SAAK,MAAM;AACX,eAAW,CAAC,KAAK,KAAK,KAAK;AAAS,WAAK,IAAI,KAAK,KAAK;AACvD,WAAO;AAAA,EACR;AAAA,EAuBO,KAAK,IAAqD,SAAkC;AAClG,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI;AAAG,eAAO;AAAA,IAChC;AAEA,WAAO;AAAA,EACR;AAAA,EAqBO,QAAQ,IAAqD,SAAkC;AACrG,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI;AAAG,eAAO;AAAA,IAChC;AAEA,WAAO;AAAA,EACR;AAAA,EAWO,MAAM,IAAqD,SAA2B;AAC5F,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,eAAe,KAAK;AAC1B,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI;AAAG,aAAK,OAAO,GAAG;AAAA,IACxC;AAEA,WAAO,eAAe,KAAK;AAAA,EAC5B;AAAA,EA0BO,OAAO,IAAqD,SAAqC;AACvG,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,UAAU,IAAI,KAAK,YAAY,OAAO,OAAO,EAAQ;AAC3D,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI;AAAG,gBAAQ,IAAI,KAAK,GAAG;AAAA,IAC7C;AAEA,WAAO;AAAA,EACR;AAAA,EAgCO,UACN,IACA,SACuC;AACvC,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,UAAgD;AAAA,MACrD,IAAI,KAAK,YAAY,OAAO,OAAO,EAAQ;AAAA,MAC3C,IAAI,KAAK,YAAY,OAAO,OAAO,EAAQ;AAAA,IAC5C;AACA,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI,GAAG;AACvB,gBAAQ,CAAC,EAAE,IAAI,KAAK,GAAG;AAAA,MACxB,OAAO;AACN,gBAAQ,CAAC,EAAE,IAAI,KAAK,GAAG;AAAA,MACxB;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA,EAkBO,QAAW,IAA8D,SAAqC;AAEpH,UAAM,cAAc,KAAK,IAAI,IAAI,OAAO;AACxC,WAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAQ,EAAE,OAAO,GAAG,WAAW;AAAA,EAC1E;AAAA,EAeO,IAAO,IAA+C,SAAwB;AACpF,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,OAAO,KAAK,QAAQ;AAC1B,WAAO,MAAM,KAAK,EAAE,QAAQ,KAAK,KAAK,GAAG,MAAS;AACjD,YAAM,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;AACjC,aAAO,GAAG,OAAO,KAAK,IAAI;AAAA,IAC3B,CAAC;AAAA,EACF;AAAA,EAeO,UAAa,IAA+C,SAAqC;AACvG,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAQ;AACxD,eAAW,CAAC,KAAK,GAAG,KAAK;AAAM,WAAK,IAAI,KAAK,GAAG,KAAK,KAAK,IAAI,CAAC;AAC/D,WAAO;AAAA,EACR;AAAA,EAeO,KAAK,IAAqD,SAA4B;AAC5F,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,GAAG,KAAK,KAAK,IAAI;AAAG,eAAO;AAAA,IAChC;AAEA,WAAO;AAAA,EACR;AAAA,EAyBO,MAAM,IAAqD,SAA4B;AAC7F,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM;AAC9B,UAAI,CAAC,GAAG,KAAK,KAAK,IAAI;AAAG,eAAO;AAAA,IACjC;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcO,OAAc,IAA+D,cAAqB;AACxG,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI;AAEJ,UAAM,WAAW,KAAK,QAAQ;AAC9B,QAAI,iBAAiB,QAAW;AAC/B,UAAI,KAAK,SAAS;AAAG,cAAM,IAAI,UAAU,kDAAkD;AAC3F,oBAAc,SAAS,KAAK,EAAE,MAAM,CAAC;AAAA,IACtC,OAAO;AACN,oBAAc;AAAA,IACf;AAEA,eAAW,CAAC,KAAK,KAAK,KAAK,UAAU;AACpC,oBAAc,GAAG,aAAa,OAAO,KAAK,IAAI;AAAA,IAC/C;AAEA,WAAO;AAAA,EACR;AAAA,EAmBO,KAAK,IAAkD,SAAyB;AACtF,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAE/C,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,SAAG,OAAO,KAAK,IAAI;AAAA,IACpB;AAEA,WAAO;AAAA,EACR;AAAA,EAiBO,IAAI,IAAgC,SAAyB;AACnE,QAAI,OAAO,OAAO;AAAY,YAAM,IAAI,UAAU,GAAG,EAAE,oBAAoB;AAC3E,QAAI,YAAY;AAAW,WAAK,GAAG,KAAK,OAAO;AAC/C,OAAG,IAAI;AACP,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,QAA0B;AAChC,WAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAE,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWO,UAAU,aAAyC;AACzD,UAAM,UAAU,KAAK,MAAM;AAC3B,eAAW,QAAQ,aAAa;AAC/B,iBAAW,CAAC,KAAK,GAAG,KAAK;AAAM,gBAAQ,IAAI,KAAK,GAAG;AAAA,IACpD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUO,OAAO,YAAsC;AACnD,QAAI,CAAC;AAAY,aAAO;AACxB,QAAI,SAAS;AAAY,aAAO;AAChC,QAAI,KAAK,SAAS,WAAW;AAAM,aAAO;AAC1C,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,UAAI,CAAC,WAAW,IAAI,GAAG,KAAK,UAAU,WAAW,IAAI,GAAG,GAAG;AAC1D,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcO,KAAK,kBAAoC,YAAW,aAAa;AACvE,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC;AAClC,YAAQ,KAAK,CAAC,GAAG,MAAc,gBAAgB,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAGtE,UAAM,MAAM;AAGZ,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AACnC,YAAM,IAAI,KAAK,KAAK;AAAA,IACrB;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,UAAa,OAAmD;AACtE,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAQ;AACxD,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO;AACjC,UAAI,KAAK,IAAI,GAAG,KAAK,OAAO,GAAG,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG;AACrD,aAAK,IAAI,KAAK,KAAK;AAAA,MACpB;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,SAAY,OAAmD;AACrE,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAQ;AACxD,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,UAAI,CAAC,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,MAAM,IAAI,GAAG,CAAC,GAAG;AACzD,aAAK,IAAI,KAAK,KAAK;AAAA,MACpB;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,WAAc,OAAuD;AAC3E,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAY;AAC5D,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO;AACjC,UAAI,CAAC,KAAK,IAAI,GAAG;AAAG,aAAK,IAAI,KAAK,KAAK;AAAA,IACxC;AAEA,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAChC,UAAI,CAAC,MAAM,IAAI,GAAG;AAAG,aAAK,IAAI,KAAK,KAAK;AAAA,IACzC;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BO,MACN,OACA,YACA,aACA,YACmB;AACnB,UAAM,OAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAQ;AACxD,UAAM,OAAO,oBAAI,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG,GAAG,MAAM,KAAK,CAAC,CAAC;AAEtD,eAAW,OAAO,MAAM;AACvB,YAAM,YAAY,KAAK,IAAI,GAAG;AAC9B,YAAM,aAAa,MAAM,IAAI,GAAG;AAEhC,UAAI,aAAa,YAAY;AAC5B,cAAM,SAAS,WAAW,KAAK,IAAI,GAAG,GAAI,MAAM,IAAI,GAAG,GAAI,GAAG;AAC9D,YAAI,OAAO;AAAM,eAAK,IAAI,KAAK,OAAO,KAAK;AAAA,MAC5C,WAAW,WAAW;AACrB,cAAM,SAAS,WAAW,KAAK,IAAI,GAAG,GAAI,GAAG;AAC7C,YAAI,OAAO;AAAM,eAAK,IAAI,KAAK,OAAO,KAAK;AAAA,MAC5C,WAAW,YAAY;AACtB,cAAM,SAAS,YAAY,MAAM,IAAI,GAAG,GAAI,GAAG;AAC/C,YAAI,OAAO;AAAM,eAAK,IAAI,KAAK,OAAO,KAAK;AAAA,MAC5C;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeO,OAAO,kBAAoC,YAAW,aAAa;AACzE,WAAO,IAAI,KAAK,YAAY,OAAO,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,OAAO,gBAAgB,IAAI,IAAI,IAAI,EAAE,CAAC;AAAA,EAC3G;AAAA,EAEO,SAAS;AAEf,WAAO,CAAC,GAAG,KAAK,OAAO,CAAC;AAAA,EACzB;AAAA,EAEA,OAAe,YAAe,YAAe,aAAwB;AACpE,WAAO,OAAO,aAAa,WAAW,KAAK,OAAO,eAAe,WAAW,IAAI;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAc,eACb,SACA,SACmB;AACnB,UAAM,OAAO,IAAI,YAAiB;AAClC,eAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AACnC,UAAI,KAAK,IAAI,GAAG,GAAG;AAClB,aAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,GAAG,GAAI,OAAO,GAAG,CAAC;AAAA,MAClD,OAAO;AACN,aAAK,IAAI,KAAK,KAAK;AAAA,MACpB;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AACD;;;AC9yBO,IAAM,UAAU;", "names": []}