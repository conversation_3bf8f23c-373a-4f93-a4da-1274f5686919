{"version": 3, "file": "lockfile.js", "sourceRoot": "", "sources": ["../../src/util/lockfile.ts"], "names": [], "mappings": ";;;;AAAA,mCAAsC;AACtC,uDAAiC;AACjC,0DAA0B;AAC1B,mDAA6B;AAC7B,2BAA4C;AAC5C,6CAAoC;AACpC,qCAAmF;AAEnF,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEtC;;GAEG;AACH,MAAM,WAAY,SAAQ,KAAK;IAC7B,YAAmB,MAAe;QAChC,KAAK,EAAE,CAAC;QADS,WAAM,GAAN,MAAM,CAAS;IAElC,CAAC;CACF;AAED,IAAY,cAiBX;AAjBD,WAAY,cAAc;IACxB;;OAEG;IACH,6DAAS,CAAA;IACT;;OAEG;IACH,6EAAiB,CAAA;IACjB;;OAEG;IACH,+DAAU,CAAA;IACV;;OAEG;IACH,yEAAe,CAAA;AACjB,CAAC,EAjBW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAiBzB;AAED,IAAY,cAGX;AAHD,WAAY,cAAc;IACxB,+BAAa,CAAA;IACb,mCAAiB,CAAA;AACnB,CAAC,EAHW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAGzB;AASD,4BAA4B;AAC5B,MAAM,mBAAoB,SAAQ,qBAAY;CAAG;AAEjD,MAAa,QAAQ;IAQnB;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAY;QAC5B,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC;QAC1B,GAAG,CAAC,uBAAuB,IAAI,GAAG,CAAC,CAAC;QAEpC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAE1C,yDAAyD;QACzD,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC,CAAC;QAE7F,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC7C,QAAQ,MAAM,EAAE;YACd,KAAK,cAAc,CAAC,eAAe,CAAC;YACpC,KAAK,cAAc,CAAC,UAAU;gBAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACnC,KAAK,cAAc,CAAC,SAAS;gBAC3B,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAClC;gBACE,MAAM,IAAI,mCAA0B,CAAC,MAAM,CAAC,CAAC;SAChD;IACH,CAAC;IAED;;;OAGG;IACO,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAY,EAAE,IAAa;QAC1D,GAAG,CAAC,wBAAwB,IAAI,iBAAiB,IAAI,GAAG,CAAC,CAAC;QAE1D,uDAAuD;QACvD,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;YACnC,OAAO,cAAc,CAAC,SAAS,CAAC;SACjC;QAED,IAAI;YACF,MAAM,QAAQ,GAAG,CAAC,MAAM,aAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChF,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI,OAAO,KAAK,OAAO,CAAC,GAAG,EAAE;gBAC3B,GAAG,CACD,+EAA+E,QAAQ,CAAC,CAAC,CAAC,GAAG,CAC9F,CAAC;gBAEF,0EAA0E;gBAC1E,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACzB,OAAO,cAAc,CAAC,SAAS,CAAC;iBACjC;gBAED,4FAA4F;gBAC5F,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;oBAClC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,cAAc,CAAC,iBAAiB;wBAClC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC;iBAC/B;gBAED,+BAA+B;gBAC/B,OAAO,cAAc,CAAC,UAAU,CAAC;aAClC;YAED,GAAG,CAAC,iEAAiE,OAAO,GAAG,CAAC,CAAC;YAEjF,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC;SAC3F;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACrD,GAAG,CAAC,4CAA4C,CAAC,CAAC;gBAElD,OAAO,cAAc,CAAC,SAAS,CAAC;aACjC;YAED,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAED;;;OAGG;IACO,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,IAAY;QAC7C,GAAG,CAAC,2CAA2C,IAAI,GAAG,CAAC,CAAC;QACxD,gDAAgD;QAChD,IAAI,QAAQ,GAA+B,SAAS,CAAC;QACrD,oHAAoH;QACpH,IAAI,OAAO,GAAoC,SAAS,CAAC;QACzD,MAAM,IAAI,OAAO,CAAO,CAAC,GAAG,EAAE,EAAE;YAC9B,OAAO,GAAG,CAAC,YAAY,EAAE,EAAE;gBACzB,IAAI,YAAY,KAAK,IAAI,EAAE;oBACzB,GAAG,EAAE,CAAC;iBACP;YACH,CAAC,CAAC;YAEF,QAAQ,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;gBAChC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC9C,GAAG,CAAC,mCAAmC,IAAI,kBAAkB,UAAU,GAAG,CAAC,CAAC;gBAE5E,IAAI,UAAU,KAAK,cAAc,CAAC,SAAS,EAAE;oBAC3C,GAAG,EAAE,CAAC;iBACP;YACH,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB;YAEhC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE;YACZ,aAAa,CAAC,QAAQ,CAAC,CAAC;SACzB;QACD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SAC5D;QAED,GAAG,CAAC,uCAAuC,IAAI,GAAG,CAAC,CAAC;QAEpD,gDAAgD;QAChD,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,gDAAgD;QAC3E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9C,GAAG,CAAC,wDAAwD,IAAI,MAAM,UAAU,EAAE,CAAC,CAAC;QAEpF,QAAQ,UAAU,EAAE;YAClB,KAAK,cAAc,CAAC,eAAe,CAAC;YACpC,KAAK,cAAc,CAAC,UAAU;gBAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAChC,KAAK,cAAc,CAAC,SAAS;gBAC3B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC/B;gBACE,MAAM,IAAI,mCAA0B,CAAC,UAAU,CAAC,CAAC;SACpD;IACH,CAAC;IAED;;;OAGG;IACO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAY;QAC5C,8EAA8E;QAC9E,GAAG,CAAC,iDAAiD,IAAI,GAAG,CAAC,CAAC;QAC9D,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAE5B,qGAAqG;QACrG,IAAI;YACF,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;gBACvC,qGAAqG;gBACrG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACxB,GAAG,CAAC,qCAAqC,IAAI,GAAG,CAAC,CAAC;oBAElD,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;iBAC7B;gBAED,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEtC,MAAM,aAAU,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;gBAEtE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;SACJ;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,GAAG,YAAY,WAAW,IAAI,GAAG,CAAC,MAAM,EAAE;gBAC5C,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aAC/B;SACF;QAED,GAAG,CAAC,2CAA2C,IAAI,GAAG,CAAC,CAAC;QAExD,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;IAaD,YAAY,IAAY,EAAE,IAAY;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC;QAC1B,GAAG,CAAC,2BAA2B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QAE7C,IAAI,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,EAAE;YAChE,GAAG,CAAC,iCAAiC,CAAC,CAAC;YAEvC,OAAO;SACR;QAED,iFAAiF;QACjF,QAAQ,MAAM,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;YACtD,KAAK,cAAc,CAAC,SAAS;gBAC3B,GAAG,CAAC,yDAAyD,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;gBAC3E,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAEhC,OAAO;YACT,KAAK,cAAc,CAAC,iBAAiB;gBACnC,GAAG,CAAC,yDAAyD,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;gBAC3E,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAE/B,OAAO;YACT,KAAK,cAAc,CAAC,UAAU;gBAC5B,MAAM,IAAI,oCAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACzD;gBACE,MAAM,IAAI,oCAA2B,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3D;IACH,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,aAAa,CAAC,SAAkB,IAAI;QAClD,OAAO,MAAM,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE;YAClD,GAAG,CAAC,4BAA4B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YAE9C,IAAI,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtC,OAAO;aACR;YAED,IAAI,MAAM,EAAE;gBACV,MAAM,aAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE;oBAClD,GAAG,CAAC,4CAA4C,MAAM,GAAG,CAAC,CAAC;gBAC7D,CAAC,CAAC,CAAC;aACJ;YAED,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvD,0EAA0E;YAC1E,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;YACtB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;;AAvPD,iDAAiD;AAC1C,cAAK,GAAgB,IAAI,GAAG,EAAE,CAAC;AACtC,0CAA0C;AACnC,eAAM,GAAwB,IAAI,mBAAmB,EAAE,CAAC;AAC/D,iDAAiD;AAC1C,cAAK,GAAU,IAAI,mBAAK,EAAE,CAAC;AANvB,4BAAQ"}