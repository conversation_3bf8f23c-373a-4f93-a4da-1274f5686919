/**
 * Pay Command
 * Refactored pay command using the new command architecture
 */

import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { createServerSuccessEmbed, addUserInfo, formatServerCoins, EMOJIS } from '../../utils/embedBuilder';
import { ensureUser } from '../../services/economyService';
import { ValidationError, DatabaseError, RateLimitError } from '../../utils/errorHandler';
import { VALIDATION } from '../../config/constants';
import { RateLimitValidator } from '../../utils/validation/ValidationUtils';
import mongoose from 'mongoose';
import User from '../../models/User';
import Transaction from '../../models/Transaction';

/**
 * Pay command implementation
 */
export class PayCommand extends BaseCommand {
  constructor() {
    super({
      name: 'pay',
      description: 'Transfer coins to another user',
      category: CommandCategory.ECONOMY,
      requiredFeatures: ['ECONOMY_SYSTEM'],
      cooldown: 5, // Longer cooldown for transfers
    });
  }

  /**
   * Customize the command builder
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    command
      .addUserOption(option =>
        option.setName('user')
          .setDescription('The user to send coins to')
          .setRequired(true))
      .addIntegerOption(option =>
        option.setName('amount')
          .setDescription('Amount of coins to send')
          .setRequired(true)
          .setMinValue(VALIDATION.MIN_TRANSACTION_AMOUNT)
          .setMaxValue(VALIDATION.MAX_TRANSACTION_AMOUNT));
  }

  /**
   * Execute the pay command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const targetUser = interaction.options.getUser('user', true);
    const amount = interaction.options.getInteger('amount', true);
    const guildId = interaction.guild?.id;

    if (!guildId) {
      throw new Error('This command can only be used in a server');
    }

    // Validation
    this.validatePayment(interaction.user.id, targetUser.id, amount, targetUser);

    // Additional rate limiting for high-value transactions
    if (amount >= 10000) {
      try {
        RateLimitValidator.validateRateLimit(interaction.user.id, 'pay_high_value', 60); // 1 minute cooldown for high-value
      } catch (error) {
        if (error instanceof ValidationError) {
          throw new RateLimitError(60); // Convert to proper rate limit error
        }
        throw error;
      }
    }

    // Check database connection
    if (mongoose.connection.readyState !== 1) {
      throw new DatabaseError('Database is not connected. Please try again in a moment.');
    }

    // Additional security: Check if users are in the same guild
    try {
      const guild = interaction.guild;
      if (guild) {
        const targetMember = await guild.members.fetch(targetUser.id).catch(() => null);
        if (!targetMember) {
          throw new ValidationError('The target user is not a member of this server.');
        }
      }
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      // If we can't verify membership, log it but continue (they might have left recently)
      this.logger.warn('Could not verify target user membership', {
        targetUserId: targetUser.id,
        guildId: guildId,
        error: error instanceof Error ? error.message : String(error)
      });
    }

    const session = await mongoose.startSession();
    this.logger.debug('Starting payment transaction', {
      senderId: interaction.user.id,
      recipientId: targetUser.id,
      amount
    });

    try {
      let senderFinalBalance = 0;

      await session.withTransaction(async () => {
        // Ensure sender exists and has sufficient balance (guild-scoped)
        const sender = await User.findOneAndUpdate(
          { discordId: interaction.user.id, guildId: guildId },
          { $setOnInsert: { discordId: interaction.user.id, guildId: guildId, balance: 0 } },
          { upsert: true, new: true, session }
        );

        if (sender.balance < amount) {
          const formattedBalance = await formatServerCoins(guildId, sender.balance);
          const formattedAmount = await formatServerCoins(guildId, amount);
          throw new ValidationError(`Insufficient balance. You have ${formattedBalance} but need ${formattedAmount}.`);
        }

        // Additional security: Prevent negative balance edge cases
        if (sender.balance - amount < 0) {
          throw new ValidationError('Transaction would result in negative balance. Please try again.');
        }

        // Log high-value transactions for monitoring
        if (amount >= 50000) {
          this.logger.warn('High-value payment transaction', {
            senderId: interaction.user.id,
            senderUsername: interaction.user.username,
            recipientId: targetUser.id,
            recipientUsername: targetUser.username,
            amount,
            senderBalance: sender.balance,
            guildId
          });
        }

        // Deduct from sender (guild-scoped)
        const updatedSender = await User.findOneAndUpdate(
          { discordId: interaction.user.id, guildId: guildId },
          { $inc: { balance: -amount } },
          { session, new: true }
        );

        senderFinalBalance = updatedSender.balance;

        // Add to recipient (create if doesn't exist, guild-scoped)
        await User.findOneAndUpdate(
          { discordId: targetUser.id, guildId: guildId },
          {
            $inc: { balance: amount },
            $setOnInsert: { discordId: targetUser.id, guildId: guildId }
          },
          { upsert: true, session }
        );

        // Create transaction records for both users (guild-scoped)
        await Transaction.create([
          {
            discordId: interaction.user.id,
            guildId: guildId,
            type: 'pay',
            amount: -amount,
            details: `Payment to ${targetUser.tag || targetUser.username}`,
            timestamp: new Date()
          },
          {
            discordId: targetUser.id,
            guildId: guildId,
            type: 'pay',
            amount: amount,
            details: `Payment from ${interaction.user.tag || interaction.user.username}`,
            timestamp: new Date()
          }
        ], { session });

        this.logger.debug('Payment transaction completed successfully', {
          senderId: interaction.user.id,
          recipientId: targetUser.id,
          amount,
          senderFinalBalance
        });
      });

      // Create success embed
      const formattedAmount = await formatServerCoins(guildId, amount);
      const formattedBalance = await formatServerCoins(guildId, senderFinalBalance);
      const embed = await createServerSuccessEmbed(guildId, 'Payment Sent Successfully!');
      embed.setDescription(
          `${EMOJIS.ECONOMY.TRANSFER} **Payment Completed**\n\n` +
          `${formattedAmount} has been sent to **${targetUser.displayName}**!\n\n` +
          `${EMOJIS.ECONOMY.MONEY} **Your New Balance:** ${formattedBalance}`
        )
        .addFields(
          {
            name: `${EMOJIS.ACTIONS.SENDER} From`,
            value: `**${interaction.user.displayName}**`,
            inline: true
          },
          {
            name: `${EMOJIS.ACTIONS.TARGET} To`,
            value: `**${targetUser.displayName}**`,
            inline: true
          },
          {
            name: `${EMOJIS.ECONOMY.COINS} Amount`,
            value: formattedAmount,
            inline: true
          }
        );

      addUserInfo(embed, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: false
      });

      this.logger.info(`Payment completed: ${interaction.user.username} sent ${amount} PLC to ${targetUser.username}`);

      // Try to notify recipient via DM
      try {
        const recipientEmbed = await createServerSuccessEmbed(guildId, 'Payment Received!');
        recipientEmbed.setDescription(
            `${EMOJIS.ECONOMY.COINS} You received ${formattedAmount} from **${interaction.user.displayName}**!`
          );

        await targetUser.send({ embeds: [recipientEmbed] });
      } catch (error) {
        // DM failed, but payment was successful
        this.logger.debug(`Failed to send payment notification DM to ${targetUser.username}`, { error });
      }

    } catch (error) {
      this.logger.error('Error executing pay command', {
        error,
        senderId: interaction.user.id,
        recipientId: targetUser.id,
        amount
      });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Validate payment parameters
   */
  private validatePayment(senderId: string, recipientId: string, amount: number, targetUser: any): void {
    // Validate sender ID
    if (!senderId || typeof senderId !== 'string' || senderId.trim().length === 0) {
      throw new ValidationError('Invalid sender ID.');
    }

    // Validate recipient ID
    if (!recipientId || typeof recipientId !== 'string' || recipientId.trim().length === 0) {
      throw new ValidationError('Invalid recipient ID.');
    }

    // Prevent self-transfers
    if (senderId === recipientId) {
      throw new ValidationError('You cannot send coins to yourself.');
    }

    // Validate amount is a number
    if (typeof amount !== 'number' || isNaN(amount)) {
      throw new ValidationError('Invalid amount. Please enter a valid number.');
    }

    // Validate amount is positive
    if (amount <= 0) {
      throw new ValidationError('Amount must be greater than 0.');
    }

    // Validate amount is within limits
    if (amount < VALIDATION.MIN_TRANSACTION_AMOUNT) {
      throw new ValidationError(`Amount must be at least ${VALIDATION.MIN_TRANSACTION_AMOUNT} coin${VALIDATION.MIN_TRANSACTION_AMOUNT === 1 ? '' : 's'}.`);
    }

    if (amount > VALIDATION.MAX_TRANSACTION_AMOUNT) {
      throw new ValidationError(`Amount cannot exceed ${VALIDATION.MAX_TRANSACTION_AMOUNT.toLocaleString()} coins.`);
    }

    // Validate amount is an integer (no decimals)
    if (!Number.isInteger(amount)) {
      throw new ValidationError('Amount must be a whole number (no decimals).');
    }

    // Validate target user is not a bot
    if (targetUser.bot) {
      throw new ValidationError('You cannot send coins to bots.');
    }

    // Validate Discord ID format
    if (!VALIDATION.DISCORD_ID_REGEX.test(senderId)) {
      throw new ValidationError('Invalid sender Discord ID format.');
    }

    if (!VALIDATION.DISCORD_ID_REGEX.test(recipientId)) {
      throw new ValidationError('Invalid recipient Discord ID format.');
    }
  }
}
