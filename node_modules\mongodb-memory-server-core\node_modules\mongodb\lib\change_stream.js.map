{"version": 3, "file": "change_stream.js", "sourceRoot": "", "sources": ["../src/change_stream.ts"], "names": [], "mappings": ";;;AAGA,6CAA0C;AAC1C,2CAAoG;AAEpG,wEAAmG;AACnG,6BAA0B;AAC1B,mCAMiB;AACjB,iDAA6C;AAC7C,+CAAoE;AAKpE,mCAA4E;AAE5E,gBAAgB;AAChB,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;AAC7C,gBAAgB;AAChB,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjC,gBAAgB;AAChB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAE7B,MAAM,qBAAqB,GAAG;IAC5B,aAAa;IACb,YAAY;IACZ,sBAAsB;IACtB,cAAc;IACd,0BAA0B;IAC1B,oBAAoB;CACZ,CAAC;AAEX,MAAM,mBAAmB,GAAG;IAC1B,UAAU,EAAE,MAAM,CAAC,YAAY,CAAC;IAChC,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC;IAC5B,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC;CAC3B,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,gCAAoB,EAAE,eAAG,EAAE,iBAAK,CAAC,CAAC;AAEhE,MAAM,qBAAqB,GACzB,6EAA6E,CAAC;AAChF,MAAM,yBAAyB,GAAG,wBAAwB,CAAC;AA4e3D;;;GAGG;AACH,MAAa,YAGX,SAAQ,+BAAuD;IAgD/D;;;;;OAKG;IACH,YACE,MAAuB,EACvB,WAAuB,EAAE,EACzB,UAA+B,EAAE;QAEjC,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;QAEjC,IAAI,MAAM,YAAY,uBAAU,EAAE;YAChC,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC,UAAU,CAAC;SAC5C;aAAM,IAAI,MAAM,YAAY,OAAE,EAAE;YAC/B,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC,QAAQ,CAAC;SAC1C;aAAM,IAAI,MAAM,YAAY,0BAAW,EAAE;YACxC,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC,OAAO,CAAC;SACzC;aAAM;YACL,MAAM,IAAI,8BAAsB,CAC9B,mGAAmG,CACpG,CAAC;SACH;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc,EAAE;YACzD,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;SACrD;QAED,wCAAwC;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAEpB,gEAAgE;QAChE,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,SAAS,CAAC,EAAE;YACjC,IAAI,SAAS,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC/E,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACjC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,SAAS,CAAC,EAAE;YACpC,IAAI,SAAS,KAAK,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;gBAC/E,IAAI,CAAC,aAAa,CAAC,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC;aACjD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB;IAChB,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7B,CAAC;IAED,8FAA8F;IAC9F,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;IAClC,CAAC;IAED,0EAA0E;IAC1E,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,4EAA4E;QAC5E,wFAAwF;QACxF,SAAS;QACT,iDAAiD;QACjD,OAAO,IAAI,EAAE;YACX,IAAI;gBACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC5C,OAAO,OAAO,CAAC;aAChB;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI;oBACF,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;iBAC7C;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAI;wBACF,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;qBACpB;oBAAC,MAAM;wBACN,gDAAgD;qBACjD;oBACD,MAAM,KAAK,CAAC;iBACb;aACF;SACF;IACH,CAAC;IAED,8DAA8D;IAC9D,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,4EAA4E;QAC5E,wFAAwF;QACxF,SAAS;QACT,iDAAiD;QACjD,OAAO,IAAI,EAAE;YACX,IAAI;gBACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACxC,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC;gBAC5D,OAAO,eAAe,CAAC;aACxB;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI;oBACF,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;iBAC7C;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAI;wBACF,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;qBACpB;oBAAC,MAAM;wBACN,gDAAgD;qBACjD;oBACD,MAAM,KAAK,CAAC;iBACb;aACF;SACF;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,4EAA4E;QAC5E,wFAAwF;QACxF,SAAS;QACT,iDAAiD;QACjD,OAAO,IAAI,EAAE;YACX,IAAI;gBACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC3C,OAAO,MAAM,IAAI,IAAI,CAAC;aACvB;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI;oBACF,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;iBAC7C;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAI;wBACF,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;qBACpB;oBAAC,MAAM;wBACN,gDAAgD;qBACjD;oBACD,MAAM,KAAK,CAAC;iBACb;aACF;SACF;IACH,CAAC;IAED,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;QAC3B,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO;SACR;QAED,IAAI;YACF,kEAAkE;YAClE,4DAA4D;YAC5D,OAAO,IAAI,EAAE;gBACX,MAAM,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;aACzB;SACF;gBAAS;YACR,IAAI;gBACF,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;aACpB;YAAC,MAAM;gBACN,+CAA+C;aAChD;SACF;IACH,CAAC;IAED,2BAA2B;IAC3B,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC7C,CAAC;IAED,8BAA8B;IAC9B,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;QAErB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI;YACF,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;SACtB;gBAAS;YACR,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;IACH,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,OAA6B;QAClC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,8BAAsB,CAAC,yBAAyB,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;QAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,gBAAgB;IACR,aAAa;QACnB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,UAAU,EAAE;YAC9B,2DAA2D;YAC3D,MAAM,IAAI,qBAAa,CACrB,gFAAgF,CACjF,CAAC;SACH;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;IAC1B,CAAC;IAED,gBAAgB;IACR,cAAc;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;YAC7B,2DAA2D;YAC3D,MAAM,IAAI,qBAAa,CACrB,gFAAgF,CACjF,CAAC;SACH;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACK,yBAAyB,CAC/B,OAAwD;QAExD,MAAM,wBAAwB,GAAG,IAAA,qBAAa,EAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QAC/E,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAmB,CAAC,OAAO,EAAE;YAC7C,wBAAwB,CAAC,oBAAoB,GAAG,IAAI,CAAC;SACtD;QACD,MAAM,QAAQ,GAAG,CAAC,EAAE,aAAa,EAAE,wBAAwB,EAAE,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEjF,MAAM,MAAM,GACV,IAAI,CAAC,IAAI,KAAK,mBAAmB,CAAC,OAAO;YACvC,CAAC,CAAE,IAAI,CAAC,MAAsB;YAC9B,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,mBAAmB,CAAC,QAAQ;gBAC5C,CAAC,CAAE,IAAI,CAAC,MAAa,CAAC,MAAM;gBAC5B,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,mBAAmB,CAAC,UAAU;oBAC9C,CAAC,CAAE,IAAI,CAAC,MAAqB,CAAC,MAAM;oBACpC,CAAC,CAAC,IAAI,CAAC;QAEX,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,uEAAuE;YACvE,MAAM,IAAI,yBAAiB,CACzB,gFAAgF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CACvG,CAAC;SACH;QAED,MAAM,kBAAkB,GAAG,IAAI,yCAAkB,CAC/C,MAAM,EACN,IAAI,CAAC,SAAS,EACd,QAAQ,EACR,OAAO,CACR,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,oBAAoB,EAAE;YACxC,kBAAkB,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;SACxD;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YAC/C,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;SACxC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,gBAAgB;IACR,0BAA0B,CAAC,KAAe;QAChD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAErC,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,gBAAgB;IACR,aAAa,CAAC,MAA4C;QAChE,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;QACtD,IAAI,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC;QAC7B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;YACzB,IAAI;gBACF,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACpD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;aACjD;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aACtC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,gBAAgB;IACR,UAAU;QAChB,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;QACzC,IAAI,YAAY,EAAE;YAChB,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3F,YAAY,CAAC,OAAO,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;IAClC,CAAC;IAED,gBAAgB;IACR,cAAc,CAAC,MAAsB;QAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;YACjB,6DAA6D;YAC7D,MAAM,IAAI,qBAAa,CAAC,yBAAyB,CAAC,CAAC;SACpD;QAED,yFAAyF;QACzF,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,6DAA6D;YAC7D,MAAM,IAAI,yBAAiB,CAAC,yBAAyB,CAAC,CAAC;SACxD;QAED,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACzB,MAAM,IAAI,8BAAsB,CAAC,qBAAqB,CAAC,CAAC;SACzD;QAED,yBAAyB;QACzB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAEzC,mFAAmF;QACnF,kFAAkF;QAClF,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,SAAS,CAAC;QAE9C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,gBAAgB;IACR,uBAAuB,CAAC,iBAA2B;QACzD,yEAAyE;QACzE,IAAI,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAE1B,IAAI,IAAA,wBAAgB,EAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE;YACnE,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YAEtC,MAAM,QAAQ,GAAG,IAAA,mBAAW,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,EAAE,oBAAoB,CAAC,EAAE;gBAC3E,IAAI,oBAAoB;oBAAE,OAAO,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;gBACpF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;SACpD;IACH,CAAC;IAED,gBAAgB;IACR,KAAK,CAAC,yBAAyB,CAAC,iBAA2B;QACjE,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;YACjB,6DAA6D;YAC7D,MAAM,IAAI,qBAAa,CAAC,yBAAyB,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,IAAA,wBAAgB,EAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE;YACpE,IAAI;gBACF,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;aACpB;YAAC,MAAM;gBACN,2BAA2B;aAC5B;YACD,MAAM,iBAAiB,CAAC;SACzB;QAED,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,QAAQ,GAAG,IAAA,mBAAW,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI;YACF,MAAM,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YACjE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;SACzE;QAAC,MAAM;YACN,oDAAoD;YACpD,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,iBAAiB,CAAC;SACzB;IACH,CAAC;;AAhZD,aAAa;AACG,qBAAQ,GAAG,oBAAQ,CAAC;AACpC,aAAa;AACG,iBAAI,GAAG,gBAAI,CAAC;AAC5B,aAAa;AACG,iBAAI,GAAG,gBAAI,CAAC;AAC5B,aAAa;AACG,kBAAK,GAAG,iBAAK,CAAC;AAC9B;;;;;GAKG;AACa,mBAAM,GAAG,kBAAM,CAAC;AAChC,aAAa;AACG,gBAAG,GAAG,eAAG,CAAC;AAC1B,aAAa;AACG,kBAAK,GAAG,iBAAK,CAAC;AAC9B;;;GAGG;AACa,iCAAoB,GAAG,gCAAoB,CAAC;AAjDjD,oCAAY"}