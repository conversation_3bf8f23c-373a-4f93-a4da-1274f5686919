{"version": 3, "file": "MongoBinary.js", "sourceRoot": "", "sources": ["../../src/util/MongoBinary.ts"], "names": [], "mappings": ";;;;AAAA,oDAAoB;AACpB,wFAAwD;AACxD,yEAAmF;AACnF,0DAA0B;AAC1B,uDAAiC;AACjC,mCAA4E;AAC5E,iDAA0C;AAC1C,yCAAsC;AACtC,qDAA6E;AAE7E,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,qBAAqB,CAAC,CAAC;AAMzC;;GAEG;AACH,MAAa,WAAW;IACtB;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAkC;QACtD,GAAG,CAAC,UAAU,CAAC,CAAC;QAChB,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QACzC,qBAAqB;QACrB,MAAM,IAAA,aAAK,EAAC,WAAW,CAAC,CAAC;QAEzB,oBAAoB;QACpB,MAAM,QAAQ,GAAG,IAAA,oBAAY,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACpD,GAAG,CAAC,wDAAwD,QAAQ,GAAG,CAAC,CAAC;QACzE,qBAAqB;QACrB,sDAAsD;QACtD,oDAAoD;QACpD,MAAM,IAAI,GAAG,MAAM,mBAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAExC,uEAAuE;QACvE,IAAI;YACF,mDAAmD;YACnD,IAAI,CAAC,+BAAc,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC5C,GAAG,CAAC,4BAA4B,OAAO,WAAW,CAAC,CAAC;gBACpD,MAAM,UAAU,GAAG,IAAI,6BAAmB,CAAC,OAAO,CAAC,CAAC;gBACpD,+BAAc,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC;aAC3E;SACF;gBAAS;YACR,GAAG,CAAC,kCAAkC,CAAC,CAAC;YACxC,cAAc;YACd,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,GAAG,CAAC,iCAAiC,CAAC,CAAC;SACxC;QAED,MAAM,SAAS,GAAG,+BAAc,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC1D,gEAAgE;QAChE,IAAA,iBAAS,EACP,OAAO,SAAS,KAAK,QAAQ,EAC7B,IAAI,KAAK,CAAC,8BAA8B,OAAO,yCAAyC,CAAC,CAC1F,CAAC;QAEF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAwB,EAAE;QAC7C,GAAG,CAAC,SAAS,CAAC,CAAC;QAEf,oGAAoG;QACpG,MAAM,OAAO,GAA8B;YACzC,GAAG,CAAC,MAAM,+BAAc,CAAC,eAAe,CAAC,IAAiC,CAAC,CAAC;YAC5E,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAA,uBAAa,EAAC,sCAAsB,CAAC,QAAQ,CAAC,IAAI,YAAE,CAAC,QAAQ,EAAE;YAC1F,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,SAAS,CAAC,CAAC;SACtF,CAAC;QAEF,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAEvE,IAAI,UAAU,GAAuB,MAAM,+BAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAEhF,+DAA+D;QAC/D,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE;YAC1B,4JAA4J;YAC5J,IAAI,CAAC,IAAA,yBAAiB,EAAC,UAAU,CAAC,EAAE;gBAClC,GAAG,CAAC,iCAAiC,UAAU,kBAAkB,CAAC,CAAC;gBACnE,MAAM,WAAW,GAAG,IAAA,yBAAS,EAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC;qBACrD,MAAM,CAAC,QAAQ,EAAE;oBAClB,kIAAkI;qBACjI,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBAE/E,IAAA,iBAAS,EACP,CAAC,IAAA,yBAAiB,EAAC,WAAW,CAAC,EAC/B,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAChE,CAAC;gBAEF,8IAA8I;gBAC9I,IAAI,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,2BAA2B,CAAC,CAAC,EAAE;oBAChF,GAAG,CAAC,qDAAqD,CAAC,CAAC;oBAC3D,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;oBAErC,IAAI,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE;wBAC9C,mHAAmH;wBACnH,OAAO,CAAC,IAAI,CACV,yDAAyD;4BACvD,4BAA4B,aAAa,KAAK;4BAC9C,4BAA4B,OAAO,CAAC,OAAO,OAAO;4BAClD,uBAAuB,CAC1B,CAAC;qBACH;iBACF;aACF;iBAAM;gBACL,MAAM,IAAI,KAAK,CACb,8JAA8J,CAC/J,CAAC;aACH;SACF;QAED,IAAA,iBAAS,EACP,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EACnC,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAC7D,CAAC;QAEF,IAAI,CAAC,UAAU,EAAE;YACf,IAAI,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,gBAAgB,CAAC,CAAC,EAAE;gBACrE,GAAG,CAAC,2DAA2D,CAAC,CAAC;gBACjE,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC3C;iBAAM;gBACL,GAAG,CAAC,yDAAyD,CAAC,CAAC;aAChE;SACF;QAED,IAAI,CAAC,UAAU,EAAE;YACf,MAAM,eAAe,GAAG,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC1F,MAAM,IAAI,KAAK,CACb,oEAAoE,UAAU,yBAAyB,eAAe,IAAI,CAC3H,CAAC;SACH;QAED,GAAG,CAAC,iCAAiC,UAAU,GAAG,CAAC,CAAC;QAEpD,OAAO,UAAU,CAAC;IACpB,CAAC;CACF;AAhID,kCAgIC;AAED,kBAAe,WAAW,CAAC"}