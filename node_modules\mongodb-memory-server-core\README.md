# mongodb-memory-server-core

[![Node.js CI](https://github.com/typegoose/mongodb-memory-server/workflows/Node.js%20CI/badge.svg)](https://github.com/typegoose/mongodb-memory-server/actions/workflows/tests.yml?query=workflow%3A%22Node.js+CI%22)
[![NPM version](https://img.shields.io/npm/v/mongodb-memory-server-core.svg)](https://www.npmjs.com/package/mongodb-memory-server-core)
[![Downloads stat](https://img.shields.io/npm/dt/mongodb-memory-server-core.svg)](http://www.npmtrends.com/mongodb-memory-server-core)
[![Commitizen friendly](https://img.shields.io/badge/commitizen-friendly-brightgreen.svg)](http://commitizen.github.io/cz-cli/)
[![TypeScript compatible](https://img.shields.io/badge/typescript-compatible-brightgreen.svg)](https://www.typescriptlang.org)
[![codecov.io](https://codecov.io/github/typegoose/mongodb-memory-server/coverage.svg?branch=master)](https://codecov.io/github/typegoose/mongodb-memory-server?branch=master)
[![Backers on Open Collective](https://opencollective.com/mongodb-memory-server/backers/badge.svg)](#backers)
[![Sponsors on Open Collective](https://opencollective.com/mongodb-memory-server/sponsors/badge.svg)](#sponsors)
[![mongodb-memory-server-core](https://snyk.io/advisor/npm-package/mongodb-memory-server-core/badge.svg)](https://snyk.io/advisor/npm-package/mongodb-memory-server-core)

Core package which contains main code. Used in all other packages.

[Full README with available options and examples](https://github.com/typegoose/mongodb-memory-server)
