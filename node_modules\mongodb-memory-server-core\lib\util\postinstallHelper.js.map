{"version": 3, "file": "postinstallHelper.js", "sourceRoot": "", "sources": ["../../src/util/postinstallHelper.ts"], "names": [], "mappings": ";AAAA,kIAAkI;AAClI,8FAA8F;;;AAE9F,2BAA6B;AAC7B,+BAA+B;AAC/B,+CAA4C;AAC5C,mDAOyB;AAEzB,IAAA,+BAAe,EAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAEtC,IAAI,CAAC,CAAC,IAAA,yBAAS,EAAC,IAAA,6BAAa,EAAC,sCAAsB,CAAC,mBAAmB,CAAC,CAAC,EAAE;IAC1E,OAAO,CAAC,GAAG,CACT,qGAAqG,CACtG,CAAC;IACF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACjB;AAED,uFAAuF;AACvF,IAAI,OAAO,IAAA,6BAAa,EAAC,sCAAsB,CAAC,aAAa,CAAC,KAAK,QAAQ,EAAE;IAC3E,OAAO,CAAC,GAAG,CAAC,iFAAiF,CAAC,CAAC;IAC/F,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACjB;AAEM,KAAK,UAAU,uBAAuB,CAC3C,OAAgB,EAChB,KAAe;IAEf,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAEhE,IAAI,CAAC,KAAK,EAAE;QACV,iCAAiC;QACjC,IAAA,+BAAe,EACb,sCAAsB,CAAC,YAAY,EACnC,IAAA,cAAO,EAAC,IAAA,YAAO,GAAE,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CACjD,CAAC;KACH;SAAM;QACL,IAAA,+BAAe,EAAC,sCAAsB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;KACrE;IAED,IAAI,OAAO,EAAE;QACX,oCAAoC;QACpC,IAAA,+BAAe,EAAC,sCAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KAC1D;IAED,OAAO,CAAC,GAAG,CAAC,IAAA,uBAAO,EAAC,sCAAsB,CAAC,gBAAgB,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,sDAAsD;IAE9H,MAAM,OAAO,GAAG,MAAM,yBAAW,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;QACxD,OAAO,CAAC,IAAI,CAAC,oDAAoD,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAChF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,mGAAmG;IACtH,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,OAAO,GAAG,CAAC,CAAC;AACnE,CAAC;AA5BD,0DA4BC"}