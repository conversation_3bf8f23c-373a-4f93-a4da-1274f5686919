{"version": 3, "file": "aggregate.js", "sourceRoot": "", "sources": ["../../src/operations/aggregate.ts"], "names": [], "mappings": ";;;AACA,oCAAqD;AAGrD,oCAAgF;AAChF,oDAAgD;AAChD,uCAImB;AACnB,2CAA+D;AAE/D,gBAAgB;AACH,QAAA,uBAAuB,GAAG,CAAU,CAAC;AAClD,MAAM,0CAA0C,GAAG,CAAU,CAAC;AA0B9D,gBAAgB;AAChB,MAAa,kBAAiC,SAAQ,kCAA2B;IAM/E,YAAY,EAAoB,EAAE,QAAoB,EAAE,OAA0B;QAChF,KAAK,CAAC,SAAS,EAAE,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAEhD,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QAE9B,gGAAgG;QAChG,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,IAAI,+BAAuB,CAAC;QAEvD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,qEAAqE;QACrE,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,OAAO,OAAO,EAAE,GAAG,KAAK,QAAQ,EAAE;YACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC3B;aAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACjD,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,MAAM,EAAE;gBACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;aAC3B;SACF;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SAC/B;aAAM;YACL,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE;YACrC,MAAM,IAAI,iCAAyB,CACjC,wEAAwE,CACzE,CAAC;SACH;QAED,IAAI,OAAO,EAAE,MAAM,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;YACjE,MAAM,IAAI,iCAAyB,CAAC,kCAAkC,CAAC,CAAC;SACzE;IACH,CAAC;IAED,IAAa,YAAY;QACvB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;IAC7B,CAAC;IAED,aAAa,CAAC,KAAe;QAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEQ,eAAe,CACtB,MAAc,EACd,OAAkC,EAClC,QAAqB;QAErB,MAAM,OAAO,GAAqB,IAAI,CAAC,OAAO,CAAC;QAC/C,MAAM,iBAAiB,GAAG,IAAA,sBAAc,EAAC,MAAM,CAAC,CAAC;QACjD,MAAM,OAAO,GAAa,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9E,IAAI,IAAI,CAAC,aAAa,IAAI,iBAAiB,GAAG,0CAA0C,EAAE;YACxF,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE;YAC3C,4BAAY,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SAChD;QAED,IAAI,OAAO,CAAC,wBAAwB,KAAK,IAAI,EAAE;YAC7C,OAAO,CAAC,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAC;SACrE;QAED,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;YAC7C,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;SAC7C;QAED,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SAC7B;QAED,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;SAC3B;QAED,iEAAiE;QACjE,gDAAgD;QAChD,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YACjC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SACnC;QAED,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;QACtC,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAC5C,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;SAC9C;QAED,KAAK,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACnE,CAAC;CACF;AAnGD,gDAmGC;AAED,IAAA,yBAAa,EAAC,kBAAkB,EAAE;IAChC,kBAAM,CAAC,cAAc;IACrB,kBAAM,CAAC,SAAS;IAChB,kBAAM,CAAC,WAAW;IAClB,kBAAM,CAAC,eAAe;CACvB,CAAC,CAAC"}