{"name": "new-find-package-json", "version": "2.0.0", "description": "Find the an package.json in the path provided upwards", "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=12.22.0"}, "files": ["lib/**/*.js", "lib/**/*.d.ts"], "scripts": {"build": "tsc -p tsconfig.build.json", "build:tests": "tsc -p tsconfig.json", "lint": "eslint -c ./.eslintrc.js './**/*.{js,ts}'", "test": "jest", "test:watch": "jest --watchAll", "test:coverage": "jest --collectCoverage", "test:watchCoverage": "jest --collectCoverage --watchAll", "watch": "tsc -w -p tsconfig.json", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/hasezoey/new-find-package-json.git"}, "author": "hasezoey <<EMAIL>> (https://github.com/hasezoey)", "contributors": ["hasezoey <<EMAIL>>"], "license": "MIT", "devDependencies": {"@commitlint/cli": "^16.3.0", "@commitlint/config-conventional": "^16.2.4", "@semantic-release/changelog": "^5.0.1", "@semantic-release/commit-analyzer": "^8.0.1", "@semantic-release/git": "^9.0.1", "@semantic-release/github": "^7.2.3", "@semantic-release/npm": "^7.1.3", "@semantic-release/release-notes-generator": "^9.0.3", "@types/debug": "^4.1.7", "@types/jest": "^27.5.1", "@types/node": "~10.17.60", "@types/tmp": "^0.2.3", "@typescript-eslint/eslint-plugin": "^5.26.0", "@typescript-eslint/parser": "^5.26.0", "commitlint": "^16.3.0", "eslint": "^8.16.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^7.0.4", "jest": "^28.1.0", "lint-staged": "^11.2.6", "prettier": "^2.6.2", "semantic-release": "^17.4.7", "tmp": "^0.2.1", "ts-jest": "^28.0.3", "typescript": "~4.4.4"}, "dependencies": {"debug": "^4.3.4"}, "publishConfig": {"access": "public"}, "keywords": ["package.json", "json", "find-package-json", "directory", "package-json", "read-package-json"], "bugs": {"url": "https://github.com/hasezoey/new-find-package-json/issues"}}