{"version": 3, "file": "MongoBinaryDownloadUrl.js", "sourceRoot": "", "sources": ["../../src/util/MongoBinaryDownloadUrl.ts"], "names": [], "mappings": ";;;;AAAA,mCAAgD;AAChD,mDAAwE;AACxE,0DAA0B;AAC1B,uDAAiC;AACjC,mCAA4C;AAC5C,6BAA0B;AAC1B,qCAOkB;AAElB,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,gCAAgC,CAAC,CAAC;AASpD,4CAA4C;AAC/B,QAAA,mBAAmB,GAAG,EAAE,CAAC,CAAC,sDAAsD;AAE7F;;GAEG;AACH,MAAa,sBAAsB;IAMjC,YAAY,IAAgC;QAC1C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IACpB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,cAAc;QAClB,MAAM,WAAW,GAAG,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,CAAC;QAEvE,IAAI,WAAW,EAAE;YACf,GAAG,CAAC,UAAU,WAAW,uBAAuB,CAAC,CAAC;YAElD,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,WAAW,CAAC,CAAC,CAAC,gCAAgC;YAElE,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;SACvB;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5C,GAAG,CAAC,UAAU,OAAO,yBAAyB,CAAC,CAAC;QAEhD,MAAM,MAAM,GACV,IAAA,6BAAa,EAAC,sCAAsB,CAAC,eAAe,CAAC,IAAI,4BAA4B,CAAC;QACxF,GAAG,CAAC,UAAU,MAAM,iBAAiB,CAAC,CAAC;QAEvC,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,MAAM,CAAC,CAAC;QAE5B,8CAA8C;QAC9C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC/B,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC;SACnC;QAED,+HAA+H;QAC/H,GAAG,CAAC,QAAQ,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,OAAO,EAAE,CAAC;QAE5D,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,MAAM,YAAY,GAAG,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,CAAC;QAExE,yCAAyC;QACzC,IAAI,CAAC,CAAC,YAAY,EAAE;YAClB,OAAO,YAAY,CAAC;SACrB;QAED,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACrB,KAAK,KAAK;gBACR,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClC,KAAK,OAAO,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClC,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpC;gBACE,MAAM,IAAI,6BAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjD;IACH,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACf,IAAI,IAAI,GAAG,WAAW,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACnD,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE;YACtC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE;gBAC7C,IAAI,IAAI,WAAW,CAAC;aACrB;iBAAM,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE;gBAC7C,IAAI,IAAI,eAAe,CAAC;aACzB;SACF;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,MAAM,CAAC;QAE/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACf,IAAI,IAAI,GAAG,aAAa,CAAC;QACzB,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE;YAC7E,IAAI,IAAI,MAAM,CAAC;SAChB;QACD,IAAI,IAAA,yBAAiB,EAAC,cAAc,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE;YAC5E,IAAI,GAAG,eAAe,CAAC,CAAC,uEAAuE;SAChG;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YAC3B,mEAAmE;YACnE,IAAI,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE;gBAC5E,GAAG,CAAC,mFAAmF,CAAC,CAAC;gBACzF,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;aACtB;iBAAM;gBACL,GAAG,CACD,iGAAiG,CAClG,CAAC;gBACF,yDAAyD;gBACzD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;aACrB;SACF;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,MAAM,CAAC;QAE5C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAA,6BAAa,EAAC,sCAAsB,CAAC,MAAM,CAAC,EAAE;YAC5D,IAAI,CAAC,EAAE,GAAG,MAAM,IAAA,aAAK,GAAE,CAAC;SACzB;QAED,IAAI,IAAA,6BAAa,EAAC,sCAAsB,CAAC,MAAM,CAAC,EAAE;YAChD,IAAI,CAAC,eAAe,EAAE,CAAC;SACxB;QAED,MAAM,QAAQ,GAAW,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAa,CAAC,CAAC;QAE1E,uEAAuE;QACvE,IAAI,IAAI,GAAG,iBAAiB,IAAI,CAAC,IAAI,EAAE,CAAC;QAExC,iCAAiC;QACjC,IAAI,CAAC,CAAC,QAAQ,EAAE;YACd,IAAI,IAAI,IAAI,QAAQ,EAAE,CAAC;SACxB;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,MAAM,CAAC;QAE/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,eAAe;QACvB,MAAM,GAAG,GAAG,IAAA,6BAAa,EAAC,sCAAsB,CAAC,MAAM,CAAC,CAAC;QAEzD,IAAI,IAAA,yBAAiB,EAAC,GAAG,CAAC,EAAE;YAC1B,OAAO;SACR;QAED,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEzB,IAAI,IAAA,yBAAiB,EAAC,MAAM,CAAC,EAAE;YAC7B,MAAM,IAAI,wBAAe,CAAC,6DAA6D,CAAC,CAAC;SAC1F;QAED,IAAI,IAAA,yBAAiB,EAAC,OAAO,CAAC,EAAE;YAC9B,MAAM,IAAI,wBAAe,CACvB,iFAAiF,CAClF,CAAC;SACH;QAED,IAAI,CAAC,EAAE,GAAG;YACR,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;SACN,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,uBAAuB,CAAC,EAAW;QACjC,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE;YAC9B,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;SACxC;aAAM,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;SACxC;aAAM,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACrC,2HAA2H;SAC5H;aAAM,IAAI,WAAW,CAAC,gCAAgC,EAAE,EAAE,CAAC,EAAE;YAC5D,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;SACtC;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE;YACrC,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;SACxC;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE;YACrC,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;SACxC;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE;YACrC,OAAO,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YAClE,4EAA4E;SAC7E;aAAM,IAAI,WAAW,CAAC,iCAAiC,EAAE,EAAE,CAAC,EAAE;YAC7D,OAAO,CAAC,IAAI,CACV,wDAAwD,EAAE,CAAC,IAAI,0CAA0C,CAC1G,CAAC;YAEF,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBACjC,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE,OAAO;aACjB,CAAC,CAAC;SACJ;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE;YACrC,uIAAuI;YACvI,OAAO,CAAC,IAAI,CACV,qDAAqD,EAAE,CAAC,IAAI,4BAA4B,CACzF,CAAC;YAEF,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBACjC,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;SACJ;aAAM,IAAI,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE;YACtC,4EAA4E;YAC5E,OAAO,CAAC,IAAI,CACV,iHAAiH,CAClH,CAAC;SACH;QAED,qDAAqD;QACrD,MAAM,IAAI,2BAAkB,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,EAAW;QAChC,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,MAAM,OAAO,GAAW,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE;YACrC,MAAM,IAAI,4BAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7C;QAED,2DAA2D;QAC3D,2FAA2F;QAC3F,MAAM,SAAS,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QAEnE,IAAI,SAAS,IAAI,OAAO,IAAI,EAAE,EAAE;YAC9B,IAAI,IAAI,IAAI,CAAC;SACd;aAAM,IAAI,OAAO,IAAI,EAAE,EAAE;YACxB,0DAA0D;YAC1D,8CAA8C;YAC9C,kFAAkF;YAClF,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC5E,GAAG,CAAC,sEAAsE,CAAC,CAAC;gBAC5E,IAAI,IAAI,IAAI,CAAC;aACd;iBAAM;gBACL,IAAI,IAAI,IAAI,CAAC;aACd;SACF;aAAM,IAAI,OAAO,IAAI,EAAE,EAAE;YACxB,IAAI,IAAI,IAAI,CAAC;SACd;aAAM,IAAI,OAAO,IAAI,CAAC,EAAE;YACvB,IAAI,IAAI,IAAI,CAAC;SACd;aAAM,IAAI,OAAO,IAAI,GAAG,EAAE;YACzB,IAAI,IAAI,IAAI,CAAC;SACd;aAAM,IAAI,OAAO,IAAI,GAAG,EAAE;YACzB,IAAI,IAAI,IAAI,CAAC;SACd;QAED,IAAI,SAAS,IAAI,OAAO,IAAI,EAAE,EAAE;YAC9B,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC5E,MAAM,IAAI,yCAAgC,CACxC,UAAU,OAAO,IAAI,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,QAAQ,EAAE,EAChD,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,mIAAmI,CACpI,CAAC;aACH;SACF;aAAM,IAAI,OAAO,IAAI,EAAE,EAAE;YACxB,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC5E,MAAM,IAAI,yCAAgC,CACxC,UAAU,OAAO,IAAI,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,QAAQ,EAAE,EAChD,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,mIAAmI,CACpI,CAAC;aACH;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,EAAW;QAChC,MAAM,SAAS,GAAW,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAEnD,MAAM,MAAM,GAAY;YACtB,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,MAAM;YACZ,kBAAkB;YAClB,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,0GAA0G;QAC1G,yGAAyG;QACzG,IAAI,SAAS,IAAI,EAAE,EAAE;YACnB,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;aAAM,IAAI,SAAS,IAAI,EAAE,EAAE;YAC1B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;aAAM,IAAI,SAAS,IAAI,EAAE,EAAE;YAC1B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;aAAM,IAAI,SAAS,IAAI,CAAC,EAAE;YACzB,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;QAED,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACH,oBAAoB,CAAC,EAAW;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC;QAClB,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;QACvB,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,qGAAqG;QACrJ,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE;YACrC,MAAM,IAAI,4BAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7C;QAED,IAAI,eAAe,EAAE;YACnB,gDAAgD;YAChD,qCAAqC;YACrC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC3B,yEAAyE;gBACzE,IAAI,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC,EAAE;oBACvC,MAAM,IAAI,yCAAgC,CACxC,QAAQ,OAAO,QAAQ,EACvB,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,8DAA8D,CAC/D,CAAC;iBACH;gBACD,yDAAyD;gBACzD,uJAAuJ;gBACvJ,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;oBAC5E,MAAM,IAAI,yCAAgC,CACxC,QAAQ,OAAO,QAAQ,EACvB,IAAI,CAAC,OAAO,EACZ,SAAS,CACV,CAAC;iBACH;gBAED,6FAA6F;gBAC7F,8BAA8B;gBAC9B,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE;oBACtF,MAAM,IAAI,yCAAgC,CACxC,QAAQ,OAAO,QAAQ,EACvB,IAAI,CAAC,OAAO,EACZ,SAAS,CACV,CAAC;iBACH;aACF;YAED,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,EAAE;gBAChD,iDAAiD;gBACjD,IAAI,IAAI,IAAI,CAAC;aACd;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,EAAE;gBAC/E,yFAAyF;gBACzF,qFAAqF;gBACrF,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,yCAAyC,CAAC,EAAE;oBAC/E,IAAI,IAAI,GAAG,CAAC;iBACb;qBAAM;oBACL,IAAI,IAAI,IAAI,CAAC;iBACd;aACF;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE;gBACtD,yFAAyF;gBACzF,qFAAqF;gBACrF,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,yCAAyC,CAAC,EAAE;oBAC/E,IAAI,IAAI,GAAG,CAAC;iBACb;qBAAM;oBACL,IAAI,IAAI,IAAI,CAAC;iBACd;aACF;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE;gBACtD,IAAI,IAAI,IAAI,CAAC;aACd;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE;gBACtD,IAAI,IAAI,IAAI,CAAC;aACd;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE;gBACtD,IAAI,IAAI,IAAI,CAAC;aACd;iBAAM;gBACL,OAAO,CAAC,IAAI,CAAC,4BAA4B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;aACtE;SACF;aAAM;YACL,OAAO,CAAC,IAAI,CAAC,gCAAgC,OAAO,GAAG,CAAC,CAAC;SAC1D;QACD,6CAA6C;QAC7C,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAClD,sGAAsG;YACtG,IAAI,IAAI,IAAI,CAAC;SACd;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,EAAW;QAChC,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,MAAM,OAAO,GAAW,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAEjD,IAAI,OAAO,IAAI,CAAC,EAAE;YAChB,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;SAC5B;QACD,mFAAmF;QAEnF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,2CAA2C;IAC3C,oBAAoB,CAAC,EAAW;QAC9B,MAAM,YAAY,GAA4B,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAEhF,OAAO,YAAY,CAAC,CAAC,CAAC,OAAO,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACtD,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,EAAW;QAChC,IAAI,QAAQ,GAAwB,SAAS,CAAC;QAC9C,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE;YACrC,MAAM,IAAI,4BAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7C;QAED,iFAAiF;QACjF;YACE,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;gBACtC,MAAM,mBAAmB,GAA2B;oBAClD,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;iBACZ,CAAC;gBAEF,QAAQ,GAAG;oBACT,EAAE,EAAE,OAAO;oBACX,IAAI,EAAE,QAAQ;oBACd,OAAO;oBACL,sDAAsD;oBACtD,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC,EAAE,CAAC;iBACrF,CAAC;aACH;YAED,IAAI,4BAA4B,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;gBAC9C,MAAM,yBAAyB,GAA2B;oBACxD,CAAC,EAAE,OAAO;oBACV,CAAC,EAAE,OAAO;oBACV,CAAC,EAAE,OAAO;oBACV,CAAC,EAAE,OAAO;oBACV,CAAC,EAAE,OAAO;iBACX,CAAC;gBAEF,oGAAoG;gBACpG,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3F,MAAM,SAAS,GAAG,eAAe,IAAI,eAAe,CAAC;gBAErD,QAAQ,GAAG;oBACT,EAAE,EAAE,OAAO;oBACX,IAAI,EAAE,QAAQ;oBACd,2DAA2D;oBAC3D,OAAO,EAAE,yBAAyB,CAAC,SAAS,CAAC,IAAI,yBAAyB,CAAC,CAAC,CAAC;iBAC9E,CAAC;aACH;SACF;QAED,IAAI,IAAA,yBAAiB,EAAC,QAAQ,CAAC,EAAE;YAC/B,uIAAuI;YACvI,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;gBAC5C,OAAO,CAAC,IAAI,CACV,oBAAoB,EAAE,CAAC,IAAI,kEAAkE;oBAC3F,gLAAgL,CACnL,CAAC;gBAEF,QAAQ,GAAG;oBACT,EAAE,EAAE,OAAO;oBACX,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,GAAG,2BAAmB,KAAK;iBACrC,CAAC;aACH;iBAAM;gBACL,QAAQ,GAAG,EAAE,CAAC;aACf;SACF;QAED,IAAI,UAAU,GAAW,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEtE,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YAC5B,OAAO,CAAC,IAAI,CAAC,qCAAqC,QAAQ,CAAC,OAAO,kBAAkB,CAAC,CAAC;YACtF,UAAU,GAAG,2BAAmB,CAAC;SAClC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YAC3B,oHAAoH;YACpH,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE;gBAC/C,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;gBAEpB,OAAO,YAAY,CAAC;aACrB;YACD,2FAA2F;YAC3F,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,CAAC,EAAE;gBACvD,OAAO,YAAY,CAAC;aACrB;SACF;QAED,IAAI,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE;YAChC,OAAO,kBAAkB,CAAC;SAC3B;QAED,iEAAiE;QACjE,qDAAqD;QACrD,IAAI,UAAU,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE;YACjE,GAAG,CACD,0CAA0C,UAAU,4DAA4D,CACjH,CAAC;YAEF,OAAO,YAAY,CAAC;SACrB;QAED,oEAAoE;QACpE,qDAAqD;QACrD,IAAI,UAAU,GAAG,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE;YAClE,GAAG,CACD,0CAA0C,UAAU,gEAAgE,CACrH,CAAC;YAEF,OAAO,YAAY,CAAC;SACrB;QAED,iFAAiF;QACjF,IAAI,UAAU,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE;YAClE,OAAO,YAAY,CAAC;SACrB;QAED,2DAA2D;QAC3D,IAAI,UAAU,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE;YAClE,OAAO,YAAY,CAAC;SACrB;QAED,8DAA8D;QAC9D;YACE,mFAAmF;YACnF;;;eAGG;YACH,MAAM,iBAAiB,GAAG,EAAE,CAAC,CAAC,kDAAkD;YAEhF,IAAI,UAAU,GAAG,iBAAiB,EAAE;gBAClC,GAAG,CACD,uCAAuC,UAAU,6DAA6D,iBAAiB,wBAAwB,CACxJ,CAAC;gBAEF,OAAO,YAAY,CAAC;aACrB;SACF;QAED,iEAAiE;QACjE,OAAO,SAAS,UAAU,IAAI,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACH,iBAAiB,CAAC,QAAgB;QAChC,QAAQ,QAAQ,EAAE;YAChB,KAAK,QAAQ;gBACX,OAAO,KAAK,CAAC;YACf,KAAK,OAAO;gBACV,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAE5C,IAAI,IAAA,yBAAiB,EAAC,OAAO,CAAC,EAAE;oBAC9B,OAAO,SAAS,CAAC;iBAClB;gBAED,OAAO,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;YAC5D,KAAK,OAAO;gBACV,OAAO,OAAO,CAAC;YACjB;gBACE,MAAM,IAAI,6BAAoB,CAAC,QAAQ,CAAC,CAAC;SAC5C;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,aAAa,CAAC,IAAY;QAC/B,QAAQ,IAAI,EAAE;YACZ,KAAK,QAAQ,CAAC;YACd,KAAK,KAAK;gBACR,OAAO,QAAQ,CAAC;YAClB,KAAK,OAAO,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,SAAS,CAAC;YACnB;gBACE,MAAM,IAAI,iCAAwB,CAAC,IAAI,CAAC,CAAC;SAC5C;IACH,CAAC;CACF;AAhoBD,wDAgoBC;AAED,kBAAe,sBAAsB,CAAC;AAEtC;;GAEG;AACH,SAAS,WAAW,CAAC,KAAa,EAAE,EAAW;IAC7C,OAAO,CACL,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACnB,CAAC,CAAC,IAAA,yBAAiB,EAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAC/F,CAAC;AACJ,CAAC;AAED,0FAA0F;AAC1F,SAAS,mBAAmB,CAAC,OAAe;IAC1C,OAAO,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5C,CAAC"}