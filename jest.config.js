/**
 * Jest Configuration for Trade System Tests
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Root directories for tests and source code
  roots: ['<rootDir>/src', '<rootDir>/tests'],

  // Test file patterns
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],

  // Transform TypeScript files
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },

  // Module file extensions
  moduleFileExtensions: ['ts', 'js', 'json'],

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],

  // Coverage configuration
  collectCoverageFrom: [
    'src/commands/economy/**/*.ts',
    'src/services/trade/**/*.ts',
    'src/services/suggestion/**/*.ts',
    'src/services/election/**/*.ts',
    'src/services/economyService.ts',
    'src/services/economy/**/*.ts',
    'src/handlers/electionButtonHandler.ts',
    'src/monitoring/**/*.ts',
    'src/resilience/**/*.ts',
    'src/security/**/*.ts',
    'src/database/**/*.ts',
    'src/utils/ElectionQueryOptimizer.ts',
    'src/utils/validation/**/*.ts',
    'src/models/trade/**/*.ts',
    'src/models/User.ts',
    'src/models/Transaction.ts',
    'src/models/Suggestion*.ts',
    'src/models/Election*.ts',
    'src/models/SnapshotBalance.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
    '!src/**/*.spec.ts'
  ],

  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/services/trade/': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    },
    './src/services/suggestion/': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/commands/economy/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },

  // Coverage reporters
  coverageReporters: ['text', 'lcov', 'html'],

  // Test timeout
  testTimeout: 30000,

  // Clear mocks between tests
  clearMocks: true,

  // Verbose output
  verbose: true,

  // Module name mapping (correct property name)
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1'
  }
};
